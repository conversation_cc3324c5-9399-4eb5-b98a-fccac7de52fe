#!/usr/bin/env node

/**
 * RAGFlow API 响应模式检查工具
 * 
 * 功能：
 * - 检查OpenAPI规范中所有响应的模式定义
 * - 验证响应内容类型和格式
 * - 识别缺少模式定义的响应
 * - 生成详细的响应质量报告
 */

const fs = require('fs');

class ResponseChecker {
  constructor() {
    this.openapiPath = 'public/openapi.json';
  }

  /**
   * 加载OpenAPI规范
   */
  loadOpenAPI() {
    if (!fs.existsSync(this.openapiPath)) {
      throw new Error(`OpenAPI文件不存在: ${this.openapiPath}`);
    }

    try {
      const content = fs.readFileSync(this.openapiPath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      throw new Error(`解析OpenAPI文件失败: ${error.message}`);
    }
  }

  /**
   * 分析响应模式
   */
  analyzeResponses(openapi) {
    const responsesWithSchema = [];
    const responsesWithoutSchema = [];
    
    if (!openapi.paths) {
      throw new Error('OpenAPI规范中没有paths定义');
    }

    Object.keys(openapi.paths).forEach(path => {
      const pathObj = openapi.paths[path];
      
      Object.keys(pathObj).forEach(method => {
        const operation = pathObj[method];
        
        if (!operation.responses) {
          console.warn(`⚠️  ${method.toUpperCase()} ${path}: 没有响应定义`);
          return;
        }

        Object.keys(operation.responses).forEach(statusCode => {
          const response = operation.responses[statusCode];
          const responseInfo = {
            path: path,
            method: method.toUpperCase(),
            statusCode: statusCode,
            description: response.description || '无描述'
          };

          if (response.content) {
            const contentTypes = Object.keys(response.content);
            let hasSchema = false;
            let schemaDetails = {};

            contentTypes.forEach(contentType => {
              const contentDef = response.content[contentType];
              if (contentDef.schema) {
                hasSchema = true;
                schemaDetails[contentType] = {
                  type: contentDef.schema.type,
                  properties: contentDef.schema.properties ? Object.keys(contentDef.schema.properties) : [],
                  hasExample: !!contentDef.schema.example
                };
              }
            });

            const responseDetail = {
              ...responseInfo,
              contentTypes: contentTypes,
              schemaDetails: schemaDetails
            };

            if (hasSchema) {
              responsesWithSchema.push(responseDetail);
            } else {
              responsesWithoutSchema.push({
                ...responseDetail,
                reason: '有内容定义但缺少模式'
              });
            }
          } else {
            // 没有内容定义的响应
            responsesWithoutSchema.push({
              ...responseInfo,
              contentTypes: [],
              reason: '没有内容定义'
            });
          }
        });
      });
    });

    return { responsesWithSchema, responsesWithoutSchema };
  }

  /**
   * 分析响应内容类型分布
   */
  analyzeContentTypes(responsesWithSchema) {
    const contentTypeStats = {};
    
    responsesWithSchema.forEach(response => {
      response.contentTypes.forEach(contentType => {
        if (!contentTypeStats[contentType]) {
          contentTypeStats[contentType] = {
            count: 0,
            responses: []
          };
        }
        contentTypeStats[contentType].count++;
        contentTypeStats[contentType].responses.push({
          path: response.path,
          method: response.method,
          statusCode: response.statusCode
        });
      });
    });

    return contentTypeStats;
  }

  /**
   * 检查响应模式质量
   */
  checkSchemaQuality(responsesWithSchema) {
    const qualityIssues = [];
    
    responsesWithSchema.forEach(response => {
      Object.keys(response.schemaDetails).forEach(contentType => {
        const schema = response.schemaDetails[contentType];
        const issues = [];
        
        // 检查基本类型定义
        if (!schema.type) {
          issues.push('缺少type定义');
        }
        
        // 检查对象属性
        if (schema.type === 'object' && schema.properties.length === 0) {
          issues.push('对象类型但没有属性定义');
        }
        
        // 检查示例
        if (!schema.hasExample && contentType === 'application/json') {
          issues.push('建议添加示例');
        }
        
        if (issues.length > 0) {
          qualityIssues.push({
            path: response.path,
            method: response.method,
            statusCode: response.statusCode,
            contentType: contentType,
            issues: issues
          });
        }
      });
    });
    
    return qualityIssues;
  }

  /**
   * 生成报告
   */
  generateReport(analysisResult) {
    const { responsesWithSchema, responsesWithoutSchema } = analysisResult;
    
    console.log('=== 响应模式检查报告 ===');
    console.log(`⏰ 检查时间: ${new Date().toLocaleString()}`);
    console.log(`📊 总响应数: ${responsesWithSchema.length + responsesWithoutSchema.length}`);
    console.log(`✅ 包含模式定义: ${responsesWithSchema.length}`);
    console.log(`❌ 缺少模式定义: ${responsesWithoutSchema.length}`);

    // 显示缺少模式的响应
    if (responsesWithoutSchema.length > 0) {
      console.log('\n❌ 缺少模式定义的响应:');
      responsesWithoutSchema.forEach((response, i) => {
        console.log(`${i + 1}. ${response.method} ${response.path} - ${response.statusCode}`);
        console.log(`   描述: ${response.description}`);
        console.log(`   原因: ${response.reason}`);
        if (response.contentTypes.length > 0) {
          console.log(`   内容类型: ${response.contentTypes.join(', ')}`);
        }
        console.log('');
      });
    } else {
      console.log('\n✅ 所有响应都有适当的模式定义');
    }

    // 内容类型分布
    const contentTypeStats = this.analyzeContentTypes(responsesWithSchema);
    console.log('\n=== 内容类型分布 ===');
    Object.keys(contentTypeStats).forEach(contentType => {
      const stats = contentTypeStats[contentType];
      console.log(`${contentType}: ${stats.count} 个响应`);
    });

    // 模式质量检查
    const qualityIssues = this.checkSchemaQuality(responsesWithSchema);
    if (qualityIssues.length > 0) {
      console.log('\n=== 模式质量建议 ===');
      qualityIssues.forEach((issue, i) => {
        console.log(`${i + 1}. ${issue.method} ${issue.path} - ${issue.statusCode} (${issue.contentType})`);
        console.log(`   建议: ${issue.issues.join(', ')}`);
      });
    } else {
      console.log('\n✅ 所有响应模式质量良好');
    }

    return {
      totalResponses: responsesWithSchema.length + responsesWithoutSchema.length,
      responsesWithSchema: responsesWithSchema.length,
      responsesWithoutSchema: responsesWithoutSchema.length,
      contentTypes: Object.keys(contentTypeStats),
      qualityIssues: qualityIssues.length
    };
  }

  /**
   * 运行检查
   */
  async check() {
    console.log('🔍 RAGFlow API 响应模式检查工具');
    console.log('===================================');

    try {
      // 加载OpenAPI规范
      console.log(`📖 正在加载: ${this.openapiPath}`);
      const openapi = this.loadOpenAPI();
      
      // 分析响应
      console.log('🔍 正在分析响应模式...');
      const analysisResult = this.analyzeResponses(openapi);
      
      // 生成报告
      const report = this.generateReport(analysisResult);
      
      console.log('\n🎉 检查完成！');
      
      return {
        success: true,
        report: report
      };
      
    } catch (error) {
      console.error(`❌ 检查失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 导出详细报告到文件
   */
  exportDetailedReport(analysisResult, outputPath = 'response-analysis-report.json') {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        summary: {
          totalResponses: analysisResult.responsesWithSchema.length + analysisResult.responsesWithoutSchema.length,
          responsesWithSchema: analysisResult.responsesWithSchema.length,
          responsesWithoutSchema: analysisResult.responsesWithoutSchema.length
        },
        responsesWithSchema: analysisResult.responsesWithSchema,
        responsesWithoutSchema: analysisResult.responsesWithoutSchema,
        contentTypeStats: this.analyzeContentTypes(analysisResult.responsesWithSchema),
        qualityIssues: this.checkSchemaQuality(analysisResult.responsesWithSchema)
      };

      fs.writeFileSync(outputPath, JSON.stringify(report, null, 2), 'utf8');
      console.log(`📄 详细报告已导出到: ${outputPath}`);
      
      return outputPath;
    } catch (error) {
      console.error(`❌ 导出报告失败: ${error.message}`);
      return null;
    }
  }
}

// 命令行接口
if (require.main === module) {
  const checker = new ResponseChecker();
  
  // 支持命令行参数
  const args = process.argv.slice(2);
  const exportReport = args.includes('--export') || args.includes('-e');
  
  checker.check().then(result => {
    if (result.success) {
      if (exportReport) {
        // 重新分析以获取详细数据用于导出
        const openapi = checker.loadOpenAPI();
        const analysisResult = checker.analyzeResponses(openapi);
        checker.exportDetailedReport(analysisResult);
      }
      
      if (result.report.responsesWithoutSchema === 0 && result.report.qualityIssues === 0) {
        console.log('\n✅ 所有响应模式都完整且质量良好！');
        process.exit(0);
      } else {
        console.log('\n⚠️  发现一些需要改进的地方，请查看上述报告');
        process.exit(1);
      }
    } else {
      process.exit(1);
    }
  });
}

module.exports = ResponseChecker;
