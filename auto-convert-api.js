#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * RAGFlow API Markdown to OpenAPI JSON 自动转换工具
 * 
 * 功能：
 * - 解析 http_api_reference.md 文件
 * - 自动提取API端点、参数、响应等信息
 * - 生成符合OpenAPI 3.0.3标准的JSON规范
 * - 支持增量更新和完整重建
 */

class MarkdownToOpenAPIConverter {
  constructor(configPath = 'converter-config.json') {
    // 加载配置
    this.config = this.loadConfig(configPath);
    this.openApiSpec = this.initializeOpenApiSpec();
  }

  /**
   * 加载配置文件
   */
  loadConfig(configPath) {
    try {
      if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configContent);
      }
    } catch (error) {
      console.warn(`⚠️  无法加载配置文件 ${configPath}: ${error.message}`);
    }

    // 返回默认配置
    return this.getDefaultConfig();
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      openapi: {
        version: "3.0.3",
        info: {
          title: "RAGFlow API",
          description: "RAGFlow的RESTful API完整参考。",
          version: "1.0.0"
        },
        servers: [
          {
            url: "http://*************:6610",
            description: "开发服务器"
          }
        ]
      },
      resourceMapping: {
        datasets: "数据集",
        documents: "文档",
        chunks: "块",
        chats: "聊天助手",
        sessions: "会话",
        agents: "代理"
      }
    };
  }

  /**
   * 初始化OpenAPI规范
   */
  initializeOpenApiSpec() {
    return {
      openapi: this.config.openapi.version,
      info: this.config.openapi.info,
      servers: this.config.openapi.servers,
      components: {
        securitySchemes: {
          bearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "API Key"
          }
        },
        parameters: {
          DatasetId: {
            name: "dataset_id",
            in: "path",
            required: true,
            schema: {
              type: "string",
              default: "0d677ada090e11f083fcf6ceb56a6e4e"
            },
            description: "数据集ID"
          },
          ChatId: {
            name: "chat_id",
            in: "path",
            required: true,
            schema: {
              type: "string",
              default: "56e07344091611f09ea7f6ceb56a6e4e"
            },
            description: "聊天ID"
          },
          ApiKey: {
            name: "Authorization",
            in: "header",
            required: true,
            schema: {
              type: "string",
              default: "Bearer ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW"
            },
            description: "API密钥"
          }
        }
      },
      security: [
        {
          bearerAuth: []
        }
      ],
      paths: {}
    };
  }

  /**
   * 解析Markdown文件
   */
  parseMarkdown(filePath) {
    console.log(`📖 正在读取文件: ${filePath}`);
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let currentSection = null;
    let currentEndpoint = null;
    let inCodeBlock = false;
    let codeBlockContent = [];
    let codeBlockType = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 检测代码块
      if (line.startsWith('```')) {
        if (!inCodeBlock) {
          inCodeBlock = true;
          codeBlockType = line.substring(3).trim();
          codeBlockContent = [];
        } else {
          inCodeBlock = false;
          if (currentEndpoint && codeBlockType === 'json') {
            this.parseJsonExample(currentEndpoint, codeBlockContent.join('\n'));
          }
          codeBlockContent = [];
          codeBlockType = null;
        }
        continue;
      }

      if (inCodeBlock) {
        codeBlockContent.push(lines[i]); // 保持原始缩进
        continue;
      }

      // 检测章节标题
      if (line.startsWith('## ')) {
        currentSection = line.substring(3).trim();
        console.log(`📂 发现章节: ${currentSection}`);
        continue;
      }

      // 检测API端点
      const endpointMatch = line.match(/^\*\*(POST|GET|PUT|DELETE)\*\* `([^`]+)`/);
      if (endpointMatch) {
        const [, method, path] = endpointMatch;
        console.log(`🔍 发现API端点: ${method} ${path}`);
        
        currentEndpoint = {
          method: method.toLowerCase(),
          path: this.normalizePath(path),
          originalPath: path,
          summary: '',
          description: '',
          parameters: [],
          requestBody: null,
          responses: {}
        };

        // 查找描述（通常在下一行或几行后）
        for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
          const nextLine = lines[j].trim();
          if (nextLine && !nextLine.startsWith('#') && !nextLine.startsWith('**') && !nextLine.startsWith('-')) {
            currentEndpoint.description = nextLine;
            break;
          }
        }

        this.addEndpointToSpec(currentEndpoint);
        continue;
      }

      // 解析参数信息
      if (currentEndpoint && line.startsWith('- `')) {
        this.parseParameter(currentEndpoint, line);
      }
    }

    console.log(`✅ 解析完成，共发现 ${Object.keys(this.openApiSpec.paths).length} 个路径`);
  }

  /**
   * 标准化路径（移除查询参数）
   */
  normalizePath(path) {
    return path.split('?')[0];
  }

  /**
   * 添加端点到OpenAPI规范
   */
  addEndpointToSpec(endpoint) {
    const { method, path, description } = endpoint;
    
    if (!this.openApiSpec.paths[path]) {
      this.openApiSpec.paths[path] = {};
    }

    // 生成操作ID
    const operationId = this.generateOperationId(method, path);
    
    // 生成摘要
    const summary = this.generateSummary(method, path, description);

    this.openApiSpec.paths[path][method] = {
      summary,
      description: description || summary,
      operationId,
      parameters: this.generateParameters(endpoint),
      responses: this.generateResponses(method, path)
    };

    // 添加请求体（如果是POST/PUT/PATCH）
    if (['post', 'put', 'patch'].includes(method)) {
      this.openApiSpec.paths[path][method].requestBody = this.generateRequestBody(path);
    }
  }

  /**
   * 生成操作ID
   */
  generateOperationId(method, path) {
    const pathParts = path.split('/').filter(part => part && !part.startsWith('{'));
    const resource = pathParts[pathParts.length - 1] || pathParts[pathParts.length - 2] || 'resource';
    
    const methodMap = {
      get: path.includes('{') && !path.endsWith('s') ? 'get' : 'list',
      post: 'create',
      put: 'update',
      delete: 'delete',
      patch: 'update'
    };

    const action = methodMap[method] || method;
    const resourceName = resource.charAt(0).toUpperCase() + resource.slice(1);
    
    return `${action}${resourceName}`;
  }

  /**
   * 生成摘要
   */
  generateSummary(method, path, description) {
    const methodMap = {
      get: path.includes('{') && !path.endsWith('s') ? '获取' : '列出',
      post: '创建',
      put: '更新',
      delete: '删除',
      patch: '更新'
    };

    const resourceMap = {
      datasets: '数据集',
      documents: '文档',
      chunks: '块',
      chats: '聊天助手',
      sessions: '会话',
      agents: '代理',
      completions: '对话',
      retrieval: '检索'
    };

    const pathParts = path.split('/').filter(part => part && !part.startsWith('{'));
    const resource = pathParts[pathParts.length - 1] || pathParts[pathParts.length - 2];
    const resourceName = resourceMap[resource] || resource;

    return `${methodMap[method] || method}${resourceName}`;
  }

  /**
   * 生成参数
   */
  generateParameters(endpoint) {
    const parameters = [];
    const { path, method } = endpoint;

    // 添加路径参数
    const pathParams = path.match(/\{([^}]+)\}/g);
    if (pathParams) {
      pathParams.forEach(param => {
        const paramName = param.slice(1, -1);
        const paramRef = this.getParameterRef(paramName);
        
        if (paramRef) {
          parameters.push({ $ref: paramRef });
        } else {
          parameters.push({
            name: paramName,
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: `${paramName}参数`
          });
        }
      });
    }

    // 添加查询参数（对于GET请求）
    if (method === 'get' && endpoint.originalPath.includes('?')) {
      const queryParams = this.extractQueryParams(endpoint.originalPath);
      parameters.push(...queryParams);
    }

    // 添加API密钥参数
    parameters.push({ $ref: '#/components/parameters/ApiKey' });

    return parameters;
  }

  /**
   * 获取参数引用
   */
  getParameterRef(paramName) {
    const refMap = {
      dataset_id: '#/components/parameters/DatasetId',
      chat_id: '#/components/parameters/ChatId'
    };
    return refMap[paramName];
  }

  /**
   * 提取查询参数
   */
  extractQueryParams(originalPath) {
    const params = [];
    const queryString = originalPath.split('?')[1];
    
    if (queryString) {
      const queryParams = queryString.split('&');
      queryParams.forEach(param => {
        const [name] = param.split('=');
        const cleanName = name.replace(/\{|\}/g, '');
        
        const paramConfig = this.getQueryParamConfig(cleanName);
        params.push({
          name: cleanName,
          in: 'query',
          ...paramConfig
        });
      });
    }

    return params;
  }

  /**
   * 获取查询参数配置
   */
  getQueryParamConfig(paramName) {
    const configs = {
      page: {
        schema: { type: 'integer', default: 1 },
        description: '指定要检索的页面。默认为1。'
      },
      page_size: {
        schema: { type: 'integer', default: 30 },
        description: '指定每页的记录数。默认为30。'
      },
      orderby: {
        schema: { type: 'string', default: 'create_time' },
        description: '指定排序字段。默认为create_time。'
      },
      desc: {
        schema: { type: 'boolean', default: true },
        description: '指定排序顺序。默认为true（降序）。'
      },
      name: {
        schema: { type: 'string' },
        description: '名称过滤条件。'
      },
      id: {
        schema: { type: 'string' },
        description: 'ID过滤条件。'
      },
      keywords: {
        schema: { type: 'string' },
        description: '关键词搜索。'
      }
    };

    return configs[paramName] || {
      schema: { type: 'string' },
      description: `${paramName}参数`
    };
  }

  /**
   * 生成请求体
   */
  generateRequestBody(path) {
    // 根据路径生成不同的请求体模式
    if (path.includes('documents') && !path.includes('chunks')) {
      return {
        required: true,
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                file: {
                  type: 'string',
                  format: 'binary',
                  description: '要上传的文件'
                }
              }
            }
          }
        }
      };
    }

    return {
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: this.generateRequestProperties(path)
          }
        }
      }
    };
  }

  /**
   * 生成请求属性
   */
  generateRequestProperties(path) {
    const properties = {};

    if (path.includes('datasets')) {
      properties.name = {
        type: 'string',
        description: '数据集名称'
      };
    }

    if (path.includes('chats')) {
      properties.name = {
        type: 'string',
        description: '聊天助手名称'
      };
      properties.dataset_ids = {
        type: 'array',
        items: { type: 'string' },
        description: '关联的数据集ID列表'
      };
    }

    if (path.includes('chunks')) {
      properties.content = {
        type: 'string',
        description: '块内容'
      };
    }

    if (path.includes('completions')) {
      properties.question = {
        type: 'string',
        description: '问题内容'
      };
      properties.stream = {
        type: 'boolean',
        description: '是否流式响应'
      };
    }

    return properties;
  }

  /**
   * 生成响应
   */
  generateResponses(method, path) {
    const responses = {
      '200': {
        description: this.getSuccessDescription(method, path),
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                code: { type: 'integer' },
                data: this.generateResponseDataSchema(method, path)
              }
            }
          }
        }
      },
      '400': {
        description: '请求错误',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                code: { type: 'integer' },
                message: { type: 'string' }
              }
            }
          }
        }
      }
    };

    // 文件下载特殊处理
    if (method === 'get' && path.includes('documents') && path.includes('{document_id}') && !path.includes('chunks')) {
      responses['200'].content = {
        'application/octet-stream': {
          schema: {
            type: 'string',
            format: 'binary'
          }
        }
      };
    }

    return responses;
  }

  /**
   * 获取成功描述
   */
  getSuccessDescription(method, path) {
    const methodMap = {
      get: '成功获取',
      post: '成功创建',
      put: '成功更新',
      delete: '成功删除'
    };

    const resourceMap = {
      datasets: '数据集',
      documents: '文档',
      chunks: '块',
      chats: '聊天助手',
      sessions: '会话',
      agents: '代理'
    };

    const pathParts = path.split('/').filter(part => part && !part.startsWith('{'));
    const resource = pathParts[pathParts.length - 1] || pathParts[pathParts.length - 2];
    const resourceName = resourceMap[resource] || resource;

    return `${methodMap[method] || '成功'}${resourceName}`;
  }

  /**
   * 生成响应数据模式
   */
  generateResponseDataSchema(method, path) {
    if (method === 'get' && !path.includes('{') && path !== '/api/v1/retrieval') {
      return {
        type: 'array',
        items: { type: 'object' }
      };
    }

    return { type: 'object' };
  }

  /**
   * 解析JSON示例
   */
  parseJsonExample(endpoint, jsonContent) {
    try {
      const jsonData = JSON.parse(jsonContent);
      // 这里可以根据JSON示例进一步完善模式定义
      console.log(`📝 解析到JSON示例: ${endpoint.method} ${endpoint.path}`);
    } catch (e) {
      // 忽略解析错误
    }
  }

  /**
   * 解析参数
   */
  parseParameter(endpoint, line) {
    const paramMatch = line.match(/- `"?([^"`]+)"?`[^:]*:\s*(.+)/);
    if (paramMatch) {
      const [, paramName, description] = paramMatch;
      endpoint.parameters.push({
        name: paramName,
        description: description.trim()
      });
    }
  }

  /**
   * 保存OpenAPI规范
   */
  saveOpenApiSpec(outputPath) {
    console.log(`💾 正在保存OpenAPI规范到: ${outputPath}`);
    
    // 创建备份
    if (fs.existsSync(outputPath)) {
      const backupPath = outputPath.replace('.json', `.backup.${Date.now()}.json`);
      fs.copyFileSync(outputPath, backupPath);
      console.log(`📋 已创建备份: ${backupPath}`);
    }

    // 保存新文件
    fs.writeFileSync(outputPath, JSON.stringify(this.openApiSpec, null, 2), 'utf8');
    console.log(`✅ OpenAPI规范已保存`);
    
    // 统计信息
    const pathCount = Object.keys(this.openApiSpec.paths).length;
    let methodCount = 0;
    Object.values(this.openApiSpec.paths).forEach(pathObj => {
      methodCount += Object.keys(pathObj).length;
    });
    
    console.log(`📊 统计信息:`);
    console.log(`   - 路径数量: ${pathCount}`);
    console.log(`   - 方法数量: ${methodCount}`);
    console.log(`   - 文件大小: ${(fs.statSync(outputPath).size / 1024).toFixed(2)} KB`);
  }

  /**
   * 验证生成的OpenAPI规范
   */
  validateOpenApiSpec() {
    console.log(`🔍 正在验证OpenAPI规范...`);
    
    const errors = [];
    const warnings = [];

    // 检查必需字段
    if (!this.openApiSpec.openapi) errors.push('缺少openapi字段');
    if (!this.openApiSpec.info) errors.push('缺少info字段');
    if (!this.openApiSpec.paths) errors.push('缺少paths字段');

    // 检查路径
    Object.entries(this.openApiSpec.paths).forEach(([path, pathObj]) => {
      Object.entries(pathObj).forEach(([method, operation]) => {
        if (!operation.summary) warnings.push(`${method.toUpperCase()} ${path}: 缺少summary`);
        if (!operation.description) warnings.push(`${method.toUpperCase()} ${path}: 缺少description`);
        if (!operation.responses) errors.push(`${method.toUpperCase()} ${path}: 缺少responses`);
      });
    });

    // 输出结果
    if (errors.length > 0) {
      console.log(`❌ 发现 ${errors.length} 个错误:`);
      errors.forEach(error => console.log(`   - ${error}`));
    }

    if (warnings.length > 0) {
      console.log(`⚠️  发现 ${warnings.length} 个警告:`);
      warnings.forEach(warning => console.log(`   - ${warning}`));
    }

    if (errors.length === 0 && warnings.length === 0) {
      console.log(`✅ OpenAPI规范验证通过`);
    }

    return { errors, warnings };
  }

  /**
   * 运行转换
   */
  async convert(inputPath, outputPath) {
    console.log(`🚀 开始转换: ${inputPath} -> ${outputPath}`);
    console.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);
    
    try {
      // 解析Markdown
      this.parseMarkdown(inputPath);
      
      // 验证规范
      const validation = this.validateOpenApiSpec();
      
      // 保存文件
      this.saveOpenApiSpec(outputPath);
      
      console.log(`🎉 转换完成！`);
      console.log(`⏰ 完成时间: ${new Date().toLocaleString()}`);
      
      return {
        success: true,
        pathCount: Object.keys(this.openApiSpec.paths).length,
        validation
      };
      
    } catch (error) {
      console.error(`❌ 转换失败: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 命令行接口
if (require.main === module) {
  const inputPath = process.argv[2] || 'http_api_reference.md';
  const outputPath = process.argv[3] || 'public/openapi.json';
  
  console.log('🔄 RAGFlow API Markdown to OpenAPI 自动转换工具');
  console.log('================================================');
  
  const converter = new MarkdownToOpenAPIConverter();
  converter.convert(inputPath, outputPath).then(result => {
    if (result.success) {
      console.log('\n🎯 转换成功完成！');
      console.log('\n📋 下一步操作:');
      console.log('   1. 运行 node analyze-api.js 验证转换结果');
      console.log('   2. 启动服务器测试: npm start');
      console.log('   3. 在浏览器中查看: http://localhost:3000');
    } else {
      process.exit(1);
    }
  });
}

module.exports = MarkdownToOpenAPIConverter;
