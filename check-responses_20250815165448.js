const fs = require('fs');

// 读取OpenAPI JSON
const openapi = JSON.parse(fs.readFileSync('public/openapi.json', 'utf8'));

console.log('=== 检查响应模式定义 ===');

const openapiPaths = Object.keys(openapi.paths);
let responsesWithoutSchema = [];
let responsesWithSchema = [];

openapiPaths.forEach(path => {
  const pathObj = openapi.paths[path];
  Object.keys(pathObj).forEach(method => {
    const operation = pathObj[method];
    if (operation.responses) {
      Object.keys(operation.responses).forEach(statusCode => {
        const response = operation.responses[statusCode];
        const responseInfo = {
          path: path,
          method: method.toUpperCase(),
          statusCode: statusCode,
          description: response.description
        };
        
        if (response.content) {
          const contentTypes = Object.keys(response.content);
          let hasSchema = false;

          contentTypes.forEach(contentType => {
            if (response.content[contentType].schema) {
              hasSchema = true;
            }
          });

          if (hasSchema) {
            responsesWithSchema.push({
              ...responseInfo,
              contentTypes: contentTypes
            });
          } else {
            responsesWithoutSchema.push({
              ...responseInfo,
              contentTypes: contentTypes,
              contentDetail: response.content
            });
          }
        } else {
          responsesWithoutSchema.push({
            ...responseInfo,
            contentTypes: ['无内容定义'],
            contentDetail: null
          });
        }
      });
    }
  });
});

console.log(`\n包含模式定义的响应: ${responsesWithSchema.length}`);
console.log(`缺少模式定义的响应: ${responsesWithoutSchema.length}`);

if (responsesWithoutSchema.length > 0) {
  console.log('\n❌ 缺少模式定义的响应:');
  responsesWithoutSchema.forEach((response, i) => {
    console.log(`${i + 1}. ${response.method} ${response.path} - ${response.statusCode}`);
    console.log(`   描述: ${response.description}`);
    console.log(`   内容类型: ${response.contentTypes.join(', ')}`);
    if (response.contentDetail) {
      console.log(`   内容详情: ${JSON.stringify(response.contentDetail, null, 2)}`);
    }
    console.log('');
  });
} else {
  console.log('✅ 所有响应都有模式定义');
}
