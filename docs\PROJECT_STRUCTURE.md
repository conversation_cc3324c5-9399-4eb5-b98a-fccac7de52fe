# RAGFlow API 项目结构

## 📁 目录结构

```
ragflow-api/
├── 📄 README.md                    # 项目主要文档
├── 📄 package.json                 # Node.js项目配置
├── 📄 http_api_reference.md        # 原始API文档（Markdown格式）
├── 🖥️ server.js                    # 主服务器文件
├── 🖥️ server-offline.js            # 离线模式服务器
├── 🔧 download-swagger-ui.js       # Swagger UI资源下载脚本
├── 📁 public/                      # 静态文件目录
│   ├── 📄 index.html              # Swagger UI主页面
│   └── 📄 openapi.json            # OpenAPI 3.0.3规范文件
├── 📁 tools/                      # 转换工具目录
│   ├── 🔧 auto-convert-api.js     # 核心转换引擎
│   ├── 🔧 convert.js              # 命令行接口工具
│   └── ⚙️ converter-config.json   # 转换工具配置文件
├── 📁 docs/                       # 文档目录
│   ├── 📖 CONVERTER_GUIDE.md      # 转换工具使用指南
│   └── 📖 PROJECT_STRUCTURE.md    # 项目结构说明（本文件）
└── 📁 swagger-ui-5.20.3/         # Swagger UI资源文件
    └── dist/                      # Swagger UI分发文件
```

## 📋 文件说明

### 核心文件

- **`README.md`** - 项目主要文档，包含安装、使用说明和功能介绍
- **`package.json`** - Node.js项目配置，定义依赖和npm脚本
- **`http_api_reference.md`** - RAGFlow API的原始Markdown文档（3085行，31个API端点）

### 服务器文件

- **`server.js`** - 主服务器，提供Swagger UI界面和API文档服务
- **`server-offline.js`** - 离线模式服务器，无需网络连接即可运行
- **`download-swagger-ui.js`** - 自动下载和配置Swagger UI资源的脚本

### 静态资源

- **`public/index.html`** - Swagger UI的HTML页面
- **`public/openapi.json`** - 转换后的OpenAPI 3.0.3规范文件（2933行）

### 转换工具 (`tools/`)

- **`auto-convert-api.js`** - 核心转换引擎
  - 智能解析Markdown API文档
  - 自动生成OpenAPI 3.0.3规范
  - 内置质量验证机制
  
- **`convert.js`** - 命令行接口工具
  - 用户友好的CLI界面
  - 支持多种操作模式
  - 详细的帮助和错误处理
  
- **`converter-config.json`** - 转换工具配置文件
  - OpenAPI基本信息配置
  - 资源映射和模板定义
  - 参数和响应格式标准化

### 文档 (`docs/`)

- **`CONVERTER_GUIDE.md`** - 转换工具详细使用指南
- **`PROJECT_STRUCTURE.md`** - 项目结构说明（本文件）

### Swagger UI资源

- **`swagger-ui-5.20.3/dist/`** - Swagger UI 5.20.3版本的分发文件

## 🔧 NPM脚本

```json
{
  "start": "node server.js",                    // 启动主服务器
  "start:offline": "node server-offline.js",   // 启动离线服务器
  "convert": "node tools/convert.js",          // 执行API文档转换
  "convert:help": "node tools/convert.js --help",      // 显示转换工具帮助
  "convert:validate": "node tools/convert.js --validate", // 验证现有OpenAPI文件
  "convert:incremental": "node tools/convert.js --incremental", // 增量更新
  "setup": "node download-swagger-ui.js",      // 设置Swagger UI资源
  "dev": "npm run convert && npm start",       // 开发模式（转换+启动）
  "build": "npm run convert",                  // 构建（执行转换）
  "test": "npm run build && echo '✅ 转换测试通过'" // 测试转换功能
}
```

## 🚀 工作流程

### 开发流程

1. **修改API文档** - 更新 `http_api_reference.md`
2. **执行转换** - 运行 `npm run convert`
3. **验证结果** - 检查生成的 `public/openapi.json`
4. **启动服务** - 运行 `npm start`
5. **测试API** - 在浏览器中访问 `http://localhost:3000`

### 部署流程

1. **构建项目** - 运行 `npm run build`
2. **验证质量** - 检查转换结果
3. **提交更改** - 提交到版本控制
4. **部署服务** - 部署到生产环境

## 📊 项目统计

- **总文件数**: ~20个核心文件
- **代码行数**: 
  - 转换工具: ~600行 JavaScript
  - 配置文件: ~200行 JSON
  - 文档: ~1000行 Markdown
- **API端点**: 31个完整的REST API端点
- **OpenAPI规范**: 2933行，完全符合3.0.3标准

## 🔄 版本控制

### 需要提交的文件

- ✅ 所有源代码文件
- ✅ 配置文件
- ✅ 文档文件
- ✅ 生成的OpenAPI规范文件

### 可忽略的文件

- ❌ 临时备份文件 (`*.backup.*`)
- ❌ 测试输出文件
- ❌ Node.js模块 (`node_modules/`)

## 🛠️ 维护指南

### 定期维护

1. **更新依赖** - 定期更新npm依赖包
2. **验证转换** - 定期运行转换验证
3. **文档同步** - 确保文档与代码同步
4. **备份重要文件** - 定期备份配置和文档

### 故障排除

1. **转换失败** - 检查Markdown格式和配置文件
2. **服务器启动失败** - 检查端口占用和依赖安装
3. **API文档显示异常** - 验证OpenAPI规范格式

## 📞 支持

如有问题或建议，请：
1. 查看 `docs/CONVERTER_GUIDE.md` 获取详细使用说明
2. 检查项目README.md中的故障排除部分
3. 提交Issue到项目仓库
