<!-- HTML for dev server -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Swagger UI</title>
  <link rel="stylesheet" type="text/css" href="/swagger-ui.css" >
  <link rel="icon" type="image/png" href="/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="/favicon-16x16.png" sizes="16x16" />
  <style>
    html
    {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *,
    *:before,
    *:after
    {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
  </style>
</head>

<body>

<div id="swagger-ui"></div>

<script src="/swagger-ui-bundle.js" charset="UTF-8"> </script>
<script src="/swagger-ui-standalone-preset.js" charset="UTF-8"> </script>
<script>
  window.onload = function() {
    window["SwaggerUIBundle"] = window["swagger-ui-bundle"]
    window["SwaggerUIStandalonePreset"] = window["swagger-ui-standalone-preset"]
    // Build a system
    const ui = SwaggerUIBundle({
      url: "https://petstore.swagger.io/v2/swagger.json",
      dom_id: '#swagger-ui',
      presets: [
        SwaggerUIBundle.presets.apis,
        SwaggerUIStandalonePreset
      ],
      plugins: [
        SwaggerUIBundle.plugins.DownloadUrl
      ],
      layout: SwaggerUIStandalonePreset ? "StandaloneLayout" : "BaseLayout",
      onComplete: () => {
        if(window.completeCount) {
          window.completeCount++
        } else {
          window.completeCount = 1
        }
      }
    })

    window.ui = ui
  }
</script>
</body>

</html>
