describe("#5129: parameter required + allowEmptyValue interactions", () => {
  describe("allowEmptyValue parameter", () => {
    const opId = "#operations-default-get_aev"
    it("should omit the parameter by default", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(".btn.execute")
        .click()
        .get(".request-url pre")
        .should("have.text", "http://localhost:3230/aev")
    })
    it("should include a value", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(`.parameters-col_description input[type=text]`)
        .type("asdf")
        .get(".btn.execute")
        .click()
        .get(".request-url pre")
        .should("have.text", "http://localhost:3230/aev?param=asdf")
    })
    it("should include an empty value when empty value box is checked", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(`.parameters-col_description input[type=checkbox]`)
        .check()
        .get(".btn.execute")
        .click()
        .get(".request-url pre")
        .should("have.text", "http://localhost:3230/aev?param=")
    })
    it("should include a value when empty value box is checked and then input is provided", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(`.parameters-col_description input[type=checkbox]`)
        .check()
        .get(`.parameters-col_description input[type=text]`)
        .type("1234")
        .get(".btn.execute")
        .click()
        .get(".request-url pre")
        .should("have.text", "http://localhost:3230/aev?param=1234")
    })
  })
  describe("allowEmptyValue + required parameter", () => {
    const opId = "#operations-default-get_aev_and_required"
    it("should refuse to execute by default", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(".btn.execute")
        .click()
        .wait(1000)
        .get(".request-url pre")
        .should("not.exist")
    })
    it("should include a value", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(`.parameters-col_description input[type=text]`)
        .type("asdf")
        .get(".btn.execute")
        .click()
        .get(".request-url pre")
        .should("have.text", "http://localhost:3230/aev/and/required?param=asdf")
    })
    it("should include an empty value when empty value box is checked", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(`.parameters-col_description input[type=checkbox]`)
        .check()
        .get(".btn.execute")
        .click()
        .get(".request-url pre")
        .should("have.text", "http://localhost:3230/aev/and/required?param=")
    })
    it("should include a value when empty value box is checked and then input is provided", () => {
      cy
        .visit("/?url=/documents/bugs/5129.yaml")
        .get(opId)
        .click()
        .get(".btn.try-out__btn")
        .click()
        .get(`.parameters-col_description input[type=checkbox]`)
        .check()
        .get(`.parameters-col_description input[type=text]`)
        .type("1234")
        .get(".btn.execute")
        .click()
        .get(".request-url pre")
        .should("have.text", "http://localhost:3230/aev/and/required?param=1234")
    })
  })
})