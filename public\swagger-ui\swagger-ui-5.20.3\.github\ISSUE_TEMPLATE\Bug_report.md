---
name: <PERSON>ug report
about: Report an issue you're experiencing

---

<!---
  Thanks for filing a bug report! 😄

  Before you submit, please read the following:

  If you're here to report a security issue, please STOP writing an issue and
  contact <NAME_EMAIL> instead!

  Search open/closed issues before submitting!

  Issues on GitHub are only related to problems of Swagger-UI itself. We'll try
  to offer support here for your use case, but we can't offer help with projects
  that use Swagger-UI indirectly, like Springfox or swagger-node.

  Likewise, we can't accept bugs in the Swagger/OpenAPI specifications
  themselves, or anything that violates the specifications.
-->

### Q&A (please complete the following information)
 - OS: [e.g. macOS]
 - Browser: [e.g. chrome, safari]
 - Version: [e.g. 22]
 - Method of installation: [e.g. npm, dist assets]
 - Swagger-UI version: [e.g. 3.10.0]
 - Swagger/OpenAPI version: [e.g. Swagger 2.0, OpenAPI 3.0]

### Content & configuration
<!--
  Provide us with a way to see what you're seeing,
  so that we can fix your issue.
-->

Example Swagger/OpenAPI definition:
```yaml
# your YAML here
```

Swagger-UI configuration options:
```js
<PERSON>wagger<PERSON>({
  // your config options here
})
```

```
?yourQueryStringConfig
```

### Describe the bug you're encountering
<!-- A clear and concise description of what the bug is. -->

### To reproduce...

Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

### Expected behavior
<!-- A clear and concise description of what you expected to happen. -->

### Screenshots
<!-- If applicable, add screenshots to help explain your problem. -->

### Additional context or thoughts
<!-- Add any other context about the problem here. -->
