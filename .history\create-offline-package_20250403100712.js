const fs = require('fs');
const path = require('path');
const child_process = require('child_process');
const archiver = require('archiver');

// 检查是否已安装archiver，如果没有则安装
try {
  require.resolve('archiver');
} catch (e) {
  console.log('正在安装打包所需依赖...');
  child_process.execSync('npm install archiver --no-save', { stdio: 'inherit' });
}

// 创建输出目录
const outputDir = path.join(__dirname, 'offline-package');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 检查本地Swagger UI是否已存在
const localSwaggerUIPath = path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-5.20.3', 'dist');
const swaggerUIPath = path.join(__dirname, 'public', 'swagger-ui');
const requiredFiles = [
  'swagger-ui.css',
  'swagger-ui-bundle.js',
  'swagger-ui-standalone-preset.js',
  'favicon-16x16.png',
  'favicon-32x32.png',
  'oauth2-redirect.html'
];

// 检查是否已经有必要的Swagger UI文件
let hasAllSwaggerUIFiles = true;
for (const file of requiredFiles) {
  if (!fs.existsSync(path.join(swaggerUIPath, file))) {
    hasAllSwaggerUIFiles = false;
    break;
  }
}

// 如果没有所有必要的Swagger UI文件，先尝试从本地Swagger UI复制
if (!hasAllSwaggerUIFiles && fs.existsSync(localSwaggerUIPath)) {
  console.log('正在从本地Swagger UI 5.20.3复制必要文件...');
  
  if (!fs.existsSync(swaggerUIPath)) {
    fs.mkdirSync(swaggerUIPath, { recursive: true });
  }
  
  for (const file of requiredFiles) {
    try {
      fs.copyFileSync(
        path.join(localSwaggerUIPath, file),
        path.join(swaggerUIPath, file)
      );
      console.log(`已复制 ${file}`);
    } catch (err) {
      console.error(`复制 ${file} 失败: ${err.message}`);
    }
  }
} else if (!hasAllSwaggerUIFiles) {
  // 如果本地没有Swagger UI，尝试从网络下载
  console.log('Swagger UI文件不完整，尝试从网络下载...');
  try {
    child_process.execSync('node download-swagger-ui.js', { stdio: 'inherit' });
  } catch (error) {
    console.error('警告: 下载Swagger UI资源失败，将创建无Swagger UI的离线包');
  }
}

// 创建zip文件
const output = fs.createWriteStream(path.join(outputDir, 'ragflow-api-docs-offline.zip'));
const archive = archiver('zip', {
  zlib: { level: 9 } // 最高压缩级别
});

// 监听错误
archive.on('error', function(err) {
  throw err;
});

// 管道连接
archive.pipe(output);

// 添加文件到归档
console.log('正在创建离线包...');

// 添加必要的文件
const filesToInclude = [
  'server-offline.js',
  'server.js',
  'download-swagger-ui.js',
  'package.json',
  'README.md',
  'start-offline.bat',
  'start-offline.sh',
  'start-with-local-swagger.bat',
  'start-with-local-swagger.sh',
  'public/index.html',
  'public/openapi.json'
];

filesToInclude.forEach(file => {
  if (fs.existsSync(file)) {
    if (fs.lstatSync(file).isDirectory()) {
      archive.directory(file, file);
    } else {
      archive.file(file, { name: file });
    }
  }
});

// 添加swagger-ui目录
if (fs.existsSync('public/swagger-ui')) {
  // 只添加必要的Swagger UI文件，不包括swagger-ui-5.20.3整个目录
  requiredFiles.forEach(file => {
    const filePath = path.join('public', 'swagger-ui', file);
    if (fs.existsSync(filePath)) {
      archive.file(filePath, { name: filePath });
    }
  });
  
  // 添加manual-install.txt
  const manualInstallPath = path.join('public', 'swagger-ui', 'manual-install.txt');
  if (fs.existsSync(manualInstallPath)) {
    archive.file(manualInstallPath, { name: manualInstallPath });
  }
}

// 添加node_modules中的express
try {
  const expressPath = path.dirname(require.resolve('express/package.json'));
  const expressParentPath = path.dirname(expressPath);
  
  // 只包含必要的依赖
  const dependencies = ['express', 'accepts', 'array-flatten', 'body-parser', 'content-disposition', 
    'content-type', 'cookie', 'cookie-signature', 'debug', 'depd', 'encodeurl', 'escape-html', 
    'etag', 'finalhandler', 'fresh', 'http-errors', 'merge-descriptors', 'methods', 
    'on-finished', 'parseurl', 'path-to-regexp', 'proxy-addr', 'qs', 'range-parser', 
    'safe-buffer', 'send', 'serve-static', 'setprototypeof', 'statuses', 'type-is', 
    'utils-merge', 'vary'];
  
  dependencies.forEach(dep => {
    try {
      const depPath = path.join(expressParentPath, dep);
      if (fs.existsSync(depPath)) {
        archive.directory(depPath, `node_modules/${dep}`);
      }
    } catch (err) {
      console.warn(`警告: 无法添加依赖 ${dep}`);
    }
  });
} catch (err) {
  console.warn('警告: 无法添加express依赖到离线包');
}

// 完成归档
archive.finalize();

output.on('close', function() {
  console.log(`成功创建离线包: ${path.join(outputDir, 'ragflow-api-docs-offline.zip')}`);
  console.log(`总大小: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`);
  console.log('\n使用说明:');
  console.log('1. 解压离线包到目标目录');
  console.log('2. 如果您已有Node.js环境，运行 npm install 安装依赖（或使用已打包的依赖）');
  console.log('3. 在Windows上运行 start-with-local-swagger.bat 或在Linux/macOS上运行 ./start-with-local-swagger.sh');
  console.log('4. 或者，也可以运行 npm run start-local');
  console.log('5. 在浏览器中访问 http://localhost:3000');
}); 