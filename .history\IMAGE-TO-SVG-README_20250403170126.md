# 图片到SVG转换工具

这是一个强大的工具，可以将UI界面图片转换为分层的、可编辑的SVG组件。这对于大屏开发特别有用，可以帮助您快速将设计稿转换为可用于开发的SVG资源。

## 特性

- 自动检测图片中的UI组件
- 为每个组件生成单独的SVG文件
- 提取图片的主题色彩
- 生成带有交互效果的示例HTML页面
- 创建CSS样式文件，方便定制
- 组件分类（按钮、面板、图表、图标等）
- 提供可视化的调试输出

## 安装

1. 确保您已安装Node.js（版本>=12）

2. 安装依赖：

```bash
npm install
```

注意：该工具依赖于`opencv4nodejs`，可能需要额外的系统依赖。如果安装过程中遇到问题，请参考：
https://github.com/justadudewhohacks/opencv4nodejs#install

对于Windows用户，可能需要先安装Visual Studio生成工具和Python。

## 使用方法

### 基本用法

```bash
npm run convert-image
```

默认情况下，它会处理项目根目录下的`image.png`文件，并将输出保存到`public/svg`目录。

### 指定输入和输出

```bash
npm run convert-image -- --input your-image.png --output-dir output/svg
```

或使用短选项：

```bash
npm run convert-image -- -i your-image.png -o output/svg
```

### 所有选项

```
选项:
  --input, -i       指定输入图片路径 (默认: image.png)
  --output-dir, -o  指定输出目录 (默认: public/svg)
  --debug-dir, -d   指定调试输出目录 (默认: public/debug)
  --colors, -c      提取的主题色数量 (默认: 5)
  --help, -h        显示帮助信息
```

## 输出文件说明

转换完成后，您将在输出目录中找到以下文件：

1. `main.svg` - 所有组件组合在一起的主SVG文件
2. `components.json` - 所有组件的信息（位置、尺寸、类型等）
3. `styles.css` - 基于组件类型生成的CSS样式文件
4. `index.html` - 带有交互功能的示例HTML页面
5. 每个检测到的组件单独的SVG文件

在调试目录中，您将找到：

1. `detected-components.png` - 标记了检测到的组件的图片
2. `component-detection-result.json` - 组件检测的详细结果

## 如何在项目中使用生成的SVG

### 在HTML中使用

```html
<!-- 方法1：直接嵌入主SVG -->
<div class="svg-container">
  <object type="image/svg+xml" data="svg/main.svg" id="main-svg"></object>
</div>

<!-- 方法2：单独使用组件 -->
<div class="component-grid">
  <img src="svg/component-panel-0.svg" class="panel-component">
  <img src="svg/component-button-1.svg" class="button-component">
</div>
```

### 在CSS中设置样式

```css
/* 导入生成的样式 */
@import 'svg/styles.css';

/* 自定义组件样式 */
.panel-component:hover {
  filter: drop-shadow(0 0 8px rgba(0,0,0,0.3));
}
```

### 使用JavaScript添加交互

```javascript
// 获取SVG文档
const svgObject = document.getElementById('main-svg');
svgObject.addEventListener('load', function() {
  const svgDoc = svgObject.contentDocument;
  
  // 获取组件
  const button = svgDoc.getElementById('component-button-1-container');
  
  // 添加事件监听器
  button.addEventListener('click', function() {
    alert('按钮被点击了！');
  });
});
```

## 技术细节

该工具使用以下关键技术：

- **OpenCV**（通过`opencv4nodejs`）：用于图像处理和组件检测
- **Canvas**：用于图像处理和操作
- **Potrace**：用于将位图转换为SVG路径
- **SVGO**：用于优化SVG输出
- **ColorThief**：用于提取图片的主题色彩

## 自定义和扩展

如果想要自定义组件检测逻辑，可以修改`advanced-component-detector.js`文件。

如果想要调整SVG生成的参数，可以修改`image-to-svg-converter.js`文件中的相关配置。

## 限制

- 组件检测目前基于简单的边缘检测和形状分析，可能无法识别所有类型的UI元素
- 文本内容将被转换为路径，不可编辑
- 复杂的渐变和阴影效果可能无法完美重现
- 转换过程对于大图片可能较慢

## 示例

以下是一个使用该工具的完整流程示例：

1. 假设您有一个数据可视化仪表板的设计图：`dashboard.png`
2. 运行转换命令：`npm run convert-image -- -i dashboard.png -o dashboard-svg`
3. 转换完成后，打开`dashboard-svg/index.html`预览效果
4. 查看`dashboard-svg/components.json`了解检测到的组件信息
5. 在您的项目中使用生成的SVG资源

## 故障排除

如果遇到以下问题：

1. **依赖安装失败**：确保您的系统满足opencv4nodejs的依赖要求
2. **组件检测不准确**：尝试使用更清晰、对比度更高的图片
3. **SVG尺寸过大**：可能是由于图片太复杂，尝试使用更简单的图片或调整阈值参数

## 贡献

欢迎对该工具进行改进和扩展！如果您有任何建议或问题，请提交Issue。 