name: Release SwaggerUI@next

on:
  workflow_dispatch:
    branches:
      - next

jobs:
  release-swagger-ui:
    name: Release SwaggerUI
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false
          ref: next

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Determine the next release version
        uses: cycjimmy/semantic-release-action@v4
        with:
          dry_run: true
          extra_plugins: |
            @semantic-release/git
            @semantic-release/exec
        env:
          GITHUB_TOKEN: ${{ secrets.SWAGGER_BOT_GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Nothing to release
        if: ${{ env.NEXT_RELEASE_VERSION == '' }}
        uses: actions/github-script@v7
        with:
          script: |
            core.setFailed('Nothing to release')

      - name: Install dependencies
        run: npm ci

      - name: Prepare release
        run: |
          npm run build

      - name: Semantic Release
        id: semantic
        uses: cycjimmy/semantic-release-action@v4
        with:
          dry_run: false
          extra_plugins: |
            @semantic-release/git
        env:
          GITHUB_TOKEN: ${{ secrets.SWAGGER_BOT_GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Release failed
        if: steps.semantic.outputs.new_release_published == 'false'
        uses: actions/github-script@v7
        with:
          script: |
            core.setFailed('Release failed')

      - name: Release published
        run: |
          echo ${{ steps.semantic.outputs.new_release_version }}
          echo ${{ steps.semantic.outputs.new_release_major_version }}
          echo ${{ steps.semantic.outputs.new_release_minor_version }}
          echo ${{ steps.semantic.outputs.new_release_patch_version }}
