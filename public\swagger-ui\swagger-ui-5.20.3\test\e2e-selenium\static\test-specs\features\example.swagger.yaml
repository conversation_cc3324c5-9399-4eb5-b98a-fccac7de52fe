swagger: "2.0"
info:
  description: |
    This is an API documentation of example.
  version: "0.1.0"
  title: Example
  termsOfService: 'http://www.example.com/terms/'
  contact:
    email: <EMAIL>
  license:
    name: Proprietary license
    url: 'http://www.example.com/license/'
paths:
  /one:
    put:
      parameters:
        - in: query
          name: NotValidParam
          type: integer
          example: 12345
          required: true
          description: This example **should not** have an effect
        - in: query
          name: ValidParam
          type: integer
          x-example: 12345
          description: This example **should** have an effect
        - in: body
          name: body
          description: property-level examples
          schema:
            type: object
            properties:
              one:
                type: string
                example: hello!
              two:
                type: object
                properties:
                  uno:
                    type: string
                    example: wow!
                  dos:
                    type: string
                    example: hey there!
        - in: body
          name: body2
          description: root schema-level example
          schema:
            type: object
            properties:
              foo:
                type: string
              bar:
                type: integer
            example: 
              foo: hey
              bar: 123
        - in: body
          name: body3
          description: nested schema-level example
          schema:
            type: object
            properties:
              one:
                type: object
                properties:
                  uno:
                    type: string
                  dos:
                    type: string
                example:
                  uno: woohoo!
                  dos: amazing!
      responses:
        '201':
          description: schema-level example
          schema:
            type: object
            properties:
              code:
                type: integer
                format: int64
              payload:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      format: int64
                      readOnly: true
                    code:
                      type: string
                    name:
                      type: string
            example:
              code: 201
              payload:
              - id: 1
                code: AE2
                name: Yono
        '202':
          description: property-level example
          schema:
            type: object
            properties:
              code:
                type: integer
                format: int64
                example: 202
              payload:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      format: int64
                      readOnly: true
                      example: 1
                    code:
                      type: string
                      example: AE2
                    name:
                      type: string
                      example: Yono