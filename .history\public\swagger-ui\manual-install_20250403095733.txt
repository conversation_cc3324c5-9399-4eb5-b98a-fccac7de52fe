离线手动安装Swagger UI

如果您无法通过download-swagger-ui.js下载资源，请按照以下步骤手动安装：

1. 访问 https://github.com/swagger-api/swagger-ui/releases/latest 下载最新的swagger-ui发布版本
2. 下载后解压缩dist目录中的以下文件到当前目录（public/swagger-ui/）：
   - swagger-ui.css
   - swagger-ui-bundle.js
   - swagger-ui-standalone-preset.js
   - favicon-16x16.png
   - favicon-32x32.png
   - oauth2-redirect.html

或者，您可以将swagger-ui-dist包离线下载并安装：

1. 在有网络的环境中执行：npm pack swagger-ui-dist@5.10.1
2. 这将创建一个文件：swagger-ui-dist-5.10.1.tgz
3. 将此文件复制到离线环境
4. 解压缩并将需要的文件复制到public/swagger-ui/目录

第三种方式：从CDN直接下载文件：

1. 在有网络的环境中，访问以下URL并保存文件：
   - https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui.css
   - https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui-bundle.js  
   - https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui-standalone-preset.js
   - https://unpkg.com/swagger-ui-dist@5.10.1/favicon-16x16.png
   - https://unpkg.com/swagger-ui-dist@5.10.1/favicon-32x32.png
   - https://unpkg.com/swagger-ui-dist@5.10.1/oauth2-redirect.html
2. 将这些文件复制到public/swagger-ui/目录 