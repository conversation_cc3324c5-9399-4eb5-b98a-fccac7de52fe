#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const ImageToSvgConverter = require('./image-to-svg-converter');

// 帮助信息
const showHelp = () => {
  console.log(`
图片到SVG转换工具
---------------

将UI界面图片转换为分层的、可编辑的SVG组件

用法:
  node convert-image.js [选项]

选项:
  --input, -i       指定输入图片路径 (默认: image.png)
  --output-dir, -o  指定输出目录 (默认: public/svg)
  --debug-dir, -d   指定调试输出目录 (默认: public/debug)
  --colors, -c      提取的主题色数量 (默认: 5)
  --help, -h        显示帮助信息

示例:
  node convert-image.js -i dashboard.png -o output/dashboard
  `);
  process.exit(0);
};

// 解析命令行参数
const parseArgs = () => {
  const args = process.argv.slice(2);
  const options = {
    inputImage: 'image.png',
    outputDir: 'public/svg',
    debugDir: 'public/debug',
    colorPaletteSize: 5
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--help' || arg === '-h') {
      showHelp();
    } else if (arg === '--input' || arg === '-i') {
      options.inputImage = args[++i];
    } else if (arg === '--output-dir' || arg === '-o') {
      options.outputDir = args[++i];
    } else if (arg === '--debug-dir' || arg === '-d') {
      options.debugDir = args[++i];
    } else if (arg === '--colors' || arg === '-c') {
      const colorCount = parseInt(args[++i], 10);
      if (!isNaN(colorCount) && colorCount > 0) {
        options.colorPaletteSize = colorCount;
      } else {
        console.error('错误: 颜色数量必须是正整数');
        process.exit(1);
      }
    }
  }

  return options;
};

// 检查依赖
const checkDependencies = () => {
  try {
    require('canvas');
    require('opencv4nodejs');
    require('potrace');
    require('svgo');
    require('colorthief');
  } catch (error) {
    console.error(`
错误: 缺少依赖项

请确保已安装以下依赖:
npm install canvas opencv4nodejs potrace svgo colorthief

对于opencv4nodejs，可能需要额外的系统依赖，参考:
https://github.com/justadudewhohacks/opencv4nodejs#install
    `);
    process.exit(1);
  }
};

// 验证输入图片
const validateInput = (inputPath) => {
  if (!fs.existsSync(inputPath)) {
    console.error(`错误: 找不到输入图片 '${inputPath}'`);
    process.exit(1);
  }

  const ext = path.extname(inputPath).toLowerCase();
  const supportedExtensions = ['.png', '.jpg', '.jpeg', '.bmp'];
  
  if (!supportedExtensions.includes(ext)) {
    console.error(`错误: 不支持的图片格式 '${ext}'。支持的格式: ${supportedExtensions.join(', ')}`);
    process.exit(1);
  }
};

// 主函数
const main = async () => {
  try {
    // 显示欢迎消息
    console.log(`
=========================================
    图片到SVG转换器 - 大屏开发助手
=========================================
`);
    
    // 检查依赖
    checkDependencies();
    
    // 解析命令行参数
    const options = parseArgs();
    
    // 验证输入
    const inputPath = path.resolve(options.inputImage);
    validateInput(inputPath);
    
    console.log(`输入图片: ${inputPath}`);
    console.log(`输出目录: ${path.resolve(options.outputDir)}`);
    console.log(`调试目录: ${path.resolve(options.debugDir)}`);
    console.log(`主题色数量: ${options.colorPaletteSize}`);
    console.log('\n开始转换...\n');
    
    // 创建并运行转换器
    const converter = new ImageToSvgConverter({
      inputImage: inputPath,
      outputDir: options.outputDir,
      debugDir: options.debugDir,
      colorPaletteSize: options.colorPaletteSize
    });
    
    const result = await converter.convert();
    
    console.log(`
=========================================
转换成功! 输出文件:

主SVG文件: ${result.mainSvgPath}
CSS样式: ${result.cssPath}
示例HTML: ${result.htmlPath}
组件数量: ${result.components.length}

您可以在浏览器中打开HTML文件查看效果:
file://${path.resolve(result.htmlPath)}
=========================================
`);
    
  } catch (error) {
    console.error('\n转换失败:', error);
    process.exit(1);
  }
};

// 运行主函数
main().catch(console.error); 