{"openapi": {"version": "3.0.3", "info": {"title": "RAGFlow API", "description": "RAGFlow的RESTful API完整参考。使用前，请确保您已准备好[RAGFlow API密钥进行身份验证](../guides/models/llm_api_key_setup.md)。", "version": "1.0.0", "contact": {"name": "RAGFlow API Support", "url": "https://github.com/infiniflow/ragflow"}, "license": {"name": "Apache 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.html"}}, "servers": [{"url": "http://*************:6610", "description": "开发服务器"}, {"url": "https://api.ragflow.io", "description": "生产服务器"}]}, "parsing": {"sectionMarkers": {"h2": "## ", "h3": "### ", "endpoint": "**{METHOD}** `{PATH}`"}, "codeBlockMarkers": {"start": "```", "end": "```"}, "parameterMarkers": {"listItem": "- `", "description": "`:"}}, "resourceMapping": {"datasets": "数据集", "documents": "文档", "chunks": "块", "chats": "聊天助手", "sessions": "会话", "agents": "代理", "completions": "对话", "retrieval": "检索"}, "methodMapping": {"get": {"single": "获取", "list": "列出", "operationPrefix": "get"}, "post": {"action": "创建", "operationPrefix": "create"}, "put": {"action": "更新", "operationPrefix": "update"}, "delete": {"action": "删除", "operationPrefix": "delete"}, "patch": {"action": "更新", "operationPrefix": "update"}}, "commonParameters": {"page": {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1, "minimum": 1}, "description": "指定要检索的页面。默认为1。"}, "page_size": {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 30, "minimum": 1, "maximum": 100}, "description": "指定每页的记录数。默认为30，最大100。"}, "orderby": {"name": "orderby", "in": "query", "schema": {"type": "string", "default": "create_time", "enum": ["create_time", "update_time", "name"]}, "description": "指定排序字段。默认为create_time。"}, "desc": {"name": "desc", "in": "query", "schema": {"type": "boolean", "default": true}, "description": "指定排序顺序。默认为true（降序）。"}, "name": {"name": "name", "in": "query", "schema": {"type": "string"}, "description": "名称过滤条件。"}, "id": {"name": "id", "in": "query", "schema": {"type": "string"}, "description": "ID过滤条件。"}, "keywords": {"name": "keywords", "in": "query", "schema": {"type": "string"}, "description": "关键词搜索。"}}, "responseTemplates": {"success": {"description": "操作成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "data": {"type": "object"}}}}}}, "successList": {"description": "获取列表成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "data": {"type": "array", "items": {"type": "object"}}}}}}}, "error": {"description": "请求错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "请求参数错误"}}}}}}, "fileDownload": {"description": "文件下载", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}, "requestBodyTemplates": {"json": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "multipart": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "要上传的文件"}}}}}}}, "validation": {"requiredFields": ["openapi", "info", "paths"], "requiredOperationFields": ["summary", "description", "responses"], "warningFields": ["operationId", "parameters"]}, "output": {"indent": 2, "createBackup": true, "backupSuffix": ".backup", "validateAfterGeneration": true}}