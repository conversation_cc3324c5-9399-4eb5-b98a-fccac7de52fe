{"name": "ragflow-api-docs", "version": "1.0.0", "description": "RAGFlow API文档转换工具 - 将Markdown API文档转换为OpenAPI JSON规范", "main": "server.js", "scripts": {"start": "node server.js", "start:offline": "node server-offline.js", "convert": "node tools/convert.js", "convert:help": "node tools/convert.js --help", "convert:validate": "node tools/convert.js --validate", "convert:incremental": "node tools/convert.js --incremental", "setup": "node download-swagger-ui.js", "dev": "npm run convert && npm start", "build": "npm run convert", "test": "npm run build && echo '✅ 转换测试通过'"}, "keywords": ["ragflow", "api", "documentation", "openapi", "swagger", "markdown", "converter"], "author": "RAGFlow API Documentation Team", "license": "Apache-2.0", "dependencies": {"express": "^4.18.2"}, "devDependencies": {}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/infiniflow/ragflow"}, "bugs": {"url": "https://github.com/infiniflow/ragflow/issues"}, "homepage": "https://github.com/infiniflow/ragflow#readme"}