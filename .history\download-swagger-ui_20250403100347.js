const fs = require('fs');
const path = require('path');
const https = require('https');

// 检查本地Swagger UI是否存在
const localSwaggerUIPath = path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-5.20.3', 'dist');
const localSwaggerUIExists = fs.existsSync(localSwaggerUIPath);

if (localSwaggerUIExists) {
  console.log('检测到本地Swagger UI 5.20.3版本，将使用本地文件...');
  
  // 要复制的文件列表
  const filesToCopy = [
    'swagger-ui.css',
    'swagger-ui-bundle.js',
    'swagger-ui-standalone-preset.js',
    'favicon-16x16.png',
    'favicon-32x32.png',
    'oauth2-redirect.html'
  ];
  
  // 确保目标目录存在
  const targetDir = path.join(__dirname, 'public', 'swagger-ui');
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }
  
  // 复制文件
  filesToCopy.forEach(file => {
    const source = path.join(localSwaggerUIPath, file);
    const destination = path.join(targetDir, file);
    
    try {
      fs.copyFileSync(source, destination);
      console.log(`已复制 ${file} 到 ${destination}`);
    } catch(err) {
      console.error(`复制 ${file} 失败: ${err.message}`);
    }
  });
  
  console.log('所有文件复制完成！');
} else {
  // 如果本地不存在，则尝试从网络下载
  console.log('未检测到本地Swagger UI，将尝试从网络下载...');
  
  const swaggerUIFiles = [
    {
      url: 'https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui.css',
      destination: path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui.css')
    },
    {
      url: 'https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui-bundle.js',
      destination: path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-bundle.js')
    },
    {
      url: 'https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui-standalone-preset.js',
      destination: path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-standalone-preset.js')
    },
    {
      url: 'https://unpkg.com/swagger-ui-dist@5.10.1/favicon-16x16.png',
      destination: path.join(__dirname, 'public', 'swagger-ui', 'favicon-16x16.png')
    },
    {
      url: 'https://unpkg.com/swagger-ui-dist@5.10.1/favicon-32x32.png',
      destination: path.join(__dirname, 'public', 'swagger-ui', 'favicon-32x32.png')
    },
    {
      url: 'https://unpkg.com/swagger-ui-dist@5.10.1/oauth2-redirect.html',
      destination: path.join(__dirname, 'public', 'swagger-ui', 'oauth2-redirect.html')
    }
  ];

  // 确保目录存在
  if (!fs.existsSync(path.join(__dirname, 'public', 'swagger-ui'))) {
    fs.mkdirSync(path.join(__dirname, 'public', 'swagger-ui'), { recursive: true });
  }

  // 下载文件
  function downloadFile(url, destination) {
    return new Promise((resolve, reject) => {
      console.log(`正在下载 ${url} 到 ${destination}...`);
      
      const file = fs.createWriteStream(destination);
      https.get(url, (response) => {
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          console.log(`已下载 ${destination}`);
          resolve();
        });
      }).on('error', (err) => {
        fs.unlink(destination, () => {}); // 删除部分下载的文件
        console.error(`下载 ${url} 失败: ${err.message}`);
        reject(err);
      });
    });
  }

  // 依次下载所有文件
  async function downloadAllFiles() {
    for (const file of swaggerUIFiles) {
      try {
        await downloadFile(file.url, file.destination);
      } catch (err) {
        console.error(`下载失败: ${err}`);
      }
    }
    console.log('所有文件下载完成！');
  }

  downloadAllFiles();
} 