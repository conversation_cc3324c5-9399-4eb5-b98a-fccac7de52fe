openapi: 3.0.4
info:
  title: Demo API
  description: First test
  termsOfService: 'http://demo.io/terms-of-service/'
  contact:
    name: Demo Support
    email: <EMAIL>
  version: 1.0.0

paths:
  /4641_1:
    get:
      summary: Returns a 200
      security:
        - api_key_1: []
      responses:
        '200':
          description: A 200
          content:
            application/text:
              schema:
                type: string
  /4641_2:
    get:
      summary: Returns a 200
      security:
        - api_key_1: []
        - api_key_2: []
      responses:
        '200':
          description: A 200
          content:
            application/text:
              schema:
                type: string

components:
  securitySchemes:
    api_key_1:
      type: apiKey
      name: api_key_1
      in: header
    api_key_2:
      type: apiKey
      name: api_key_2
      in: header
