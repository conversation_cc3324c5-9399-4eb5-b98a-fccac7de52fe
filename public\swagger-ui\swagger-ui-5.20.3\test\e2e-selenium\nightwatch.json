{"src_folders": ["test/e2e-selenium/scenarios"], "output_folder": "reports", "live_output": true, "custom_commands_path": "", "custom_assertions_path": "", "page_objects_path": "test/e2e-selenium/pages", "globals_path": "", "test_runner": {"type": "mocha", "options": {"ui": "bdd", "reporter": "list"}}, "webdriver": {"start_process": true, "server_path": "node_modules/.bin/chromedriver", "port": 9515}, "test_settings": {"default": {"desiredCapabilities": {"browserName": "chrome"}}}}