#!/usr/bin/env node

/**
 * RAGFlow API 分析工具
 * 
 * 功能：
 * - 分析Markdown API文档和OpenAPI JSON的完整性
 * - 验证API端点匹配度
 * - 检查OpenAPI规范符合性
 * - 生成详细的分析报告
 */

const fs = require('fs');

class APIAnalyzer {
  constructor() {
    this.markdownPath = 'http_api_reference.md';
    this.openapiPath = 'public/openapi.json';
  }

  /**
   * 分析Markdown文档中的API端点
   */
  analyzeMarkdown() {
    console.log('=== Markdown API分析 ===');
    
    if (!fs.existsSync(this.markdownPath)) {
      console.error(`❌ Markdown文件不存在: ${this.markdownPath}`);
      return [];
    }

    const content = fs.readFileSync(this.markdownPath, 'utf8');
    const lines = content.split('\n');
    const apiEndpoints = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      const endpointMatch = line.match(/^\*\*(POST|GET|PUT|DELETE)\*\* `([^`]+)`/);
      
      if (endpointMatch) {
        const [, method, path] = endpointMatch;
        apiEndpoints.push({
          method: method,
          path: path,
          line: i + 1
        });
      }
    }

    console.log(`Markdown中发现 ${apiEndpoints.length} 个API端点:`);
    apiEndpoints.forEach((endpoint, i) => {
      console.log(`${i + 1}. ${endpoint.method} ${endpoint.path} (行 ${endpoint.line})`);
    });

    return apiEndpoints;
  }

  /**
   * 分析OpenAPI JSON中的路径
   */
  analyzeOpenAPI() {
    console.log('\n=== OpenAPI JSON分析 ===');
    
    if (!fs.existsSync(this.openapiPath)) {
      console.error(`❌ OpenAPI文件不存在: ${this.openapiPath}`);
      return { paths: [], methods: [] };
    }

    try {
      const content = fs.readFileSync(this.openapiPath, 'utf8');
      const openapi = JSON.parse(content);
      
      const paths = Object.keys(openapi.paths || {});
      const methods = [];
      
      console.log(`OpenAPI JSON中有 ${paths.length} 个路径:`);
      
      paths.forEach((path, i) => {
        const pathMethods = Object.keys(openapi.paths[path]);
        pathMethods.forEach(method => {
          methods.push({
            method: method.toUpperCase(),
            path: path
          });
        });
        console.log(`${i + 1}. ${path} [${pathMethods.join(', ').toUpperCase()}]`);
      });

      console.log(`\n总计 ${methods.length} 个方法`);
      
      return { paths, methods, openapi };
      
    } catch (error) {
      console.error(`❌ 解析OpenAPI文件失败: ${error.message}`);
      return { paths: [], methods: [] };
    }
  }

  /**
   * 对比分析完整性
   */
  compareCompleteness(markdownEndpoints, openapiData) {
    console.log('\n=== 转换完整性分析 ===');
    
    const { methods: openapiMethods } = openapiData;
    
    console.log(`Markdown API端点: ${markdownEndpoints.length}`);
    console.log(`OpenAPI 方法总数: ${openapiMethods.length}`);

    if (markdownEndpoints.length === openapiMethods.length) {
      console.log('✅ API端点数量匹配');
    } else {
      console.log('❌ API端点数量不匹配');
      console.log(`差异: ${Math.abs(markdownEndpoints.length - openapiMethods.length)} 个端点`);
    }

    // 找出缺失的端点
    this.findMissingEndpoints(markdownEndpoints, openapiMethods);
  }

  /**
   * 找出缺失的API端点
   */
  findMissingEndpoints(markdownEndpoints, openapiMethods) {
    console.log('\n=== 缺失的API端点分析 ===');

    // 标准化路径以便比较
    const normalizePath = (path) => path.split('?')[0];

    // 找出Markdown中有但OpenAPI中没有的端点
    const missingInOpenAPI = markdownEndpoints.filter(mdEndpoint => {
      const normalizedMdPath = normalizePath(mdEndpoint.path);
      return !openapiMethods.some(apiEndpoint => 
        apiEndpoint.method === mdEndpoint.method && 
        normalizePath(apiEndpoint.path) === normalizedMdPath
      );
    });

    // 找出OpenAPI中有但Markdown中没有的端点
    const extraInOpenAPI = openapiMethods.filter(apiEndpoint => {
      const normalizedApiPath = normalizePath(apiEndpoint.path);
      return !markdownEndpoints.some(mdEndpoint => 
        mdEndpoint.method === apiEndpoint.method && 
        normalizePath(mdEndpoint.path) === normalizedApiPath
      );
    });

    if (missingInOpenAPI.length > 0) {
      console.log('❌ Markdown中有但OpenAPI中缺失的端点:');
      missingInOpenAPI.forEach((endpoint, i) => {
        console.log(`  ${i + 1}. ${endpoint.method} ${endpoint.path}`);
      });
    } else {
      console.log('✅ 没有缺失的端点');
    }

    if (extraInOpenAPI.length > 0) {
      console.log('\n⚠️  OpenAPI中有但Markdown中没有的端点:');
      extraInOpenAPI.forEach((endpoint, i) => {
        console.log(`  ${i + 1}. ${endpoint.method} ${endpoint.path}`);
      });
    }
  }

  /**
   * 验证OpenAPI规范结构
   */
  validateOpenAPIStructure(openapiData) {
    console.log('\n=== OpenAPI规范结构验证 ===');
    
    const { openapi } = openapiData;
    if (!openapi) return;

    console.log(`OpenAPI版本: ${openapi.openapi}`);
    console.log(`API标题: ${openapi.info?.title}`);
    console.log(`API版本: ${openapi.info?.version}`);
    console.log(`服务器数量: ${openapi.servers ? openapi.servers.length : 0}`);
    console.log(`安全方案数量: ${openapi.components?.securitySchemes ? Object.keys(openapi.components.securitySchemes).length : 0}`);
    console.log(`参数组件数量: ${openapi.components?.parameters ? Object.keys(openapi.components.parameters).length : 0}`);

    // 验证必需字段
    const requiredFields = ['openapi', 'info', 'paths'];
    const missingFields = requiredFields.filter(field => !openapi[field]);

    if (missingFields.length === 0) {
      console.log('✅ 所有必需字段都存在');
    } else {
      console.log('❌ 缺少必需字段:', missingFields.join(', '));
    }
  }

  /**
   * 检查路径定义完整性
   */
  checkPathDefinitions(openapiData) {
    console.log('\n=== 路径定义完整性检查 ===');
    
    const { openapi } = openapiData;
    if (!openapi?.paths) return;

    let pathsWithIssues = 0;
    const paths = Object.keys(openapi.paths);

    paths.forEach(path => {
      const pathObj = openapi.paths[path];
      Object.keys(pathObj).forEach(method => {
        const operation = pathObj[method];
        const issues = [];
        
        if (!operation.summary) issues.push('缺少summary');
        if (!operation.description) issues.push('缺少description');
        if (!operation.operationId) issues.push('缺少operationId');
        if (!operation.responses) issues.push('缺少responses');
        
        if (issues.length > 0) {
          console.log(`⚠️  ${method.toUpperCase()} ${path}: ${issues.join(', ')}`);
          pathsWithIssues++;
        }
      });
    });

    if (pathsWithIssues === 0) {
      console.log('✅ 所有路径定义都完整');
    } else {
      console.log(`❌ ${pathsWithIssues} 个操作存在问题`);
    }
  }

  /**
   * 检查响应模式
   */
  checkResponseSchemas(openapiData) {
    console.log('\n=== 响应模式验证 ===');
    
    const { openapi } = openapiData;
    if (!openapi?.paths) return;

    let responsesWithSchema = 0;
    let responsesWithoutSchema = 0;
    const responsesWithoutSchemaDetails = [];

    Object.keys(openapi.paths).forEach(path => {
      const pathObj = openapi.paths[path];
      Object.keys(pathObj).forEach(method => {
        const operation = pathObj[method];
        if (operation.responses) {
          Object.keys(operation.responses).forEach(statusCode => {
            const response = operation.responses[statusCode];
            if (response.content && response.content['application/json'] && response.content['application/json'].schema) {
              responsesWithSchema++;
            } else {
              responsesWithoutSchema++;
              responsesWithoutSchemaDetails.push({
                path: path,
                method: method.toUpperCase(),
                statusCode: statusCode,
                description: response.description,
                hasContent: !!response.content,
                contentTypes: response.content ? Object.keys(response.content) : []
              });
            }
          });
        }
      });
    });

    console.log(`响应包含模式定义: ${responsesWithSchema}`);
    console.log(`响应缺少模式定义: ${responsesWithoutSchema}`);

    if (responsesWithoutSchema === 0) {
      console.log('✅ 所有响应都有模式定义');
    } else {
      console.log(`⚠️  ${responsesWithoutSchema} 个响应缺少模式定义`);
      console.log('\n缺少application/json模式的响应:');
      responsesWithoutSchemaDetails.forEach((response, i) => {
        console.log(`${i + 1}. ${response.method} ${response.path} - ${response.statusCode}`);
        console.log(`   描述: ${response.description}`);
        console.log(`   有内容: ${response.hasContent}`);
        console.log(`   内容类型: ${response.contentTypes.join(', ')}`);
        console.log('');
      });
    }
  }

  /**
   * 运行完整分析
   */
  async analyze() {
    console.log('🔍 RAGFlow API 完整性分析工具');
    console.log('=====================================');
    console.log(`⏰ 分析时间: ${new Date().toLocaleString()}\n`);

    try {
      // 分析Markdown文档
      const markdownEndpoints = this.analyzeMarkdown();
      
      // 分析OpenAPI JSON
      const openapiData = this.analyzeOpenAPI();
      
      // 对比完整性
      this.compareCompleteness(markdownEndpoints, openapiData);
      
      // 验证OpenAPI结构
      this.validateOpenAPIStructure(openapiData);
      
      // 检查路径定义
      this.checkPathDefinitions(openapiData);
      
      // 检查响应模式
      this.checkResponseSchemas(openapiData);
      
      console.log('\n🎉 分析完成！');
      
      return {
        markdownEndpoints: markdownEndpoints.length,
        openapiMethods: openapiData.methods.length,
        isComplete: markdownEndpoints.length === openapiData.methods.length
      };
      
    } catch (error) {
      console.error(`❌ 分析过程中发生错误: ${error.message}`);
      return { error: error.message };
    }
  }
}

// 命令行接口
if (require.main === module) {
  const analyzer = new APIAnalyzer();
  analyzer.analyze().then(result => {
    if (result.error) {
      process.exit(1);
    } else if (result.isComplete) {
      console.log('\n✅ API文档转换完整，质量良好！');
      process.exit(0);
    } else {
      console.log('\n⚠️  发现不完整或不一致的地方，请检查上述分析结果');
      process.exit(1);
    }
  });
}

module.exports = APIAnalyzer;
