const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const ColorThief = require('colorthief');
const potrace = require('potrace');
const SVGO = require('svgo');

// 配置
const config = {
  inputImage: 'image.png',
  outputDir: 'public/svg',
  componentDetectionThreshold: 20, // 组件检测阈值
  colorPaletteSize: 5, // 主题色数量
  svgOptimization: true, // 是否优化SVG
};

// 创建输出目录
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * 图片分析和SVG生成流程
 */
async function processImage() {
  try {
    console.log('开始分析图片...');
    
    // 1. 加载图片
    const imagePath = path.resolve(config.inputImage);
    const image = await loadImage(imagePath);
    
    // 2. 创建画布
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0);
    
    // 3. 获取图片数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    
    // 4. 提取主题色
    const dominantColors = await extractDominantColors(imagePath);
    console.log('主题色:', dominantColors);
    
    // 5. 检测组件
    const components = detectComponents(imageData, canvas.width, canvas.height);
    console.log(`检测到 ${components.length} 个组件`);
    
    // 6. 为每个组件生成SVG
    const svgComponents = await Promise.all(components.map((component, index) => 
      generateComponentSVG(component, index, canvas)
    ));
    
    // 7. 生成主SVG文件
    generateMainSVG(svgComponents, image.width, image.height, dominantColors);
    
    console.log('SVG生成完成!');
    console.log(`输出目录: ${path.resolve(config.outputDir)}`);
    
  } catch (error) {
    console.error('处理图片时出错:', error);
  }
}

/**
 * 提取图片主题色
 */
async function extractDominantColors(imagePath) {
  try {
    const colors = await ColorThief.getPalette(imagePath, config.colorPaletteSize);
    return colors.map(color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`);
  } catch (error) {
    console.error('提取主题色时出错:', error);
    return ['#ffffff', '#f0f0f0', '#e0e0e0', '#d0d0d0', '#c0c0c0'];
  }
}

/**
 * 检测图片中的组件
 */
function detectComponents(imageData, width, height) {
  // 这里实现组件检测算法
  // 简化版：使用边缘检测和连通区域分析
  console.log('正在分析图片组件...');
  
  // 示例：模拟组件检测结果
  // 在实际应用中，这里应该实现一个更复杂的算法
  const components = [
    { id: 'background', type: 'background', x: 0, y: 0, width, height },
    { id: 'header', type: 'panel', x: 0, y: 0, width, height: height * 0.1 },
    { id: 'sidebar', type: 'panel', x: 0, y: height * 0.1, width: width * 0.2, height: height * 0.9 },
    { id: 'main-content', type: 'panel', x: width * 0.2, y: height * 0.1, width: width * 0.8, height: height * 0.9 },
    // 更多组件...
  ];
  
  return components;
}

/**
 * 为组件生成SVG
 */
async function generateComponentSVG(component, index, canvas) {
  const { id, type, x, y, width, height } = component;
  const fileName = `${id}.svg`;
  const filePath = path.join(config.outputDir, fileName);
  
  // 从原图中裁剪组件区域
  const ctx = canvas.getContext('2d');
  const componentImageData = ctx.getImageData(x, y, width, height);
  
  // 创建组件画布
  const componentCanvas = createCanvas(width, height);
  const componentCtx = componentCanvas.getContext('2d');
  componentCtx.putImageData(componentImageData, 0, 0);
  
  // 将组件转换为SVG
  const svgData = await canvasToSVG(componentCanvas, id);
  
  // 保存SVG文件
  fs.writeFileSync(filePath, svgData);
  
  return {
    id,
    type,
    x,
    y,
    width,
    height,
    fileName
  };
}

/**
 * 将Canvas转换为SVG
 */
async function canvasToSVG(canvas, id) {
  return new Promise((resolve, reject) => {
    const pngBuffer = canvas.toBuffer('image/png');
    
    const traceOptions = {
      threshold: 128,
      background: '#ffffff',
      color: 'auto',
    };
    
    potrace.trace(pngBuffer, traceOptions, (err, svg) => {
      if (err) {
        reject(err);
        return;
      }
      
      // 优化SVG
      if (config.svgOptimization) {
        const svgo = new SVGO({
          plugins: [
            { removeViewBox: false },
            { cleanupAttrs: true },
            { removeDoctype: true },
            { removeXMLProcInst: true },
            { removeComments: true },
            { removeMetadata: true },
            { removeTitle: true },
            { removeDesc: true },
            { removeUselessDefs: true },
            { removeEditorsNSData: true },
            { removeEmptyAttrs: true },
            { removeHiddenElems: true },
            { removeEmptyText: true },
            { removeEmptyContainers: true },
            { cleanupEnableBackground: true },
            { minifyStyles: true },
            { convertColors: true },
            { convertPathData: true },
            { convertTransform: true },
            { removeUnknownsAndDefaults: true },
            { removeNonInheritableGroupAttrs: true },
            { removeUselessStrokeAndFill: true },
            { removeUnusedNS: true },
            { cleanupIDs: true },
            { cleanupNumericValues: true },
            { moveElemsAttrsToGroup: true },
            { moveGroupAttrsToElems: true },
            { collapseGroups: true },
            { removeRasterImages: false },
            { mergePaths: true },
            { convertShapeToPath: true },
            { sortAttrs: true },
            { transformsWithOnePath: false },
            { removeDimensions: true },
            { removeAttrs: false },
          ]
        });
        
        svgo.optimize(svg).then(result => {
          // 添加ID和类名以便于CSS样式和JS交互
          const svgWithId = result.data.replace('<svg', `<svg id="${id}" class="component ${id}"`);
          resolve(svgWithId);
        }).catch(reject);
      } else {
        // 添加ID和类名
        const svgWithId = svg.replace('<svg', `<svg id="${id}" class="component ${id}"`);
        resolve(svgWithId);
      }
    });
  });
}

/**
 * 生成主SVG文件
 */
function generateMainSVG(components, width, height, colors) {
  const fileName = 'main.svg';
  const filePath = path.join(config.outputDir, fileName);
  
  let svgContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义渐变、滤镜等 -->
    <linearGradient id="background-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="${colors[0]}" />
      <stop offset="100%" stop-color="${colors[1]}" />
    </linearGradient>
    <filter id="drop-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
      <feOffset dx="2" dy="2" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="${width}" height="${height}" fill="url(#background-gradient)" />
  
  <!-- 组件引用 -->
  ${components.map(component => {
    const { id, x, y, width, height, fileName } = component;
    return `<g id="${id}-container" transform="translate(${x}, ${y})">
    <use href="#${id}" width="${width}" height="${height}" />
  </g>`;
  }).join('\n  ')}
</svg>`;

  // 保存主SVG文件
  fs.writeFileSync(filePath, svgContent);
  
  // 创建组件引用文件
  const componentsRegistry = {
    width,
    height,
    colors,
    components
  };
  
  fs.writeFileSync(
    path.join(config.outputDir, 'components.json'),
    JSON.stringify(componentsRegistry, null, 2)
  );
}

// 运行主流程
processImage().catch(console.error);

module.exports = {
  processImage
}; 