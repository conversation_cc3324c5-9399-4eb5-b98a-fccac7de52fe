openapi: 3.0.4
info:
  title: XML array schema with array-level example
  version: 1.0.0

paths:
  /users:
    get:
      responses:
        "200":
          description: ''
          content:
            application/xml:
              schema:
                $ref: '#/components/schemas/Users'

components:
  schemas:
    Users:
      type: array
      example:
        - id: 123
          name: bob
        - id: 456
          name: jane
      xml:
        name: Users
        wrapped: true
      items:
        type: object
        xml:
          name: User
        properties:
          id:
            type: integer
            xml:
              attribute: true
          name:
            type: string
            xml:
              attribute: true
