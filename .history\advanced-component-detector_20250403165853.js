const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const cv = require('opencv4nodejs');

/**
 * 高级组件检测器
 * 使用计算机视觉技术来检测UI组件
 */
class AdvancedComponentDetector {
  constructor(options = {}) {
    this.options = {
      cannyThreshold1: 50,
      cannyThreshold2: 150,
      dilationSize: 3,
      minAreaSize: 500,
      maxAreaSize: 100000,
      ...options
    };
  }

  /**
   * 从图片中检测UI组件
   * @param {string} imagePath - 图片路径
   * @returns {Array} 检测到的组件数组
   */
  async detectComponents(imagePath) {
    try {
      console.log(`正在分析图片: ${imagePath}`);
      
      // 1. 读取图片
      const image = await cv.imreadAsync(imagePath);
      const imageSize = image.sizes;
      console.log(`图片尺寸: ${imageSize[1]}x${imageSize[0]}`);
      
      // 2. 转换为灰度图
      const grayImage = image.cvtColor(cv.COLOR_BGR2GRAY);
      
      // 3. 边缘检测
      const edges = grayImage.canny(
        this.options.cannyThreshold1, 
        this.options.cannyThreshold2
      );
      
      // 4. 膨胀操作，连接边缘
      const kernel = cv.getStructuringElement(
        cv.MORPH_RECT,
        new cv.Size(this.options.dilationSize, this.options.dilationSize)
      );
      const dilated = edges.dilate(kernel);
      
      // 5. 查找轮廓
      const contours = dilated.findContours(cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);
      console.log(`检测到 ${contours.length} 个轮廓`);
      
      // 6. 过滤轮廓，只保留可能是UI组件的区域
      const filteredContours = contours.filter(contour => {
        const area = contour.area;
        return area > this.options.minAreaSize && area < this.options.maxAreaSize;
      });
      
      console.log(`过滤后剩余 ${filteredContours.length} 个潜在UI组件`);
      
      // 7. 从轮廓提取组件信息
      const components = filteredContours.map((contour, index) => {
        const boundingRect = contour.boundingRect();
        const x = boundingRect.x;
        const y = boundingRect.y;
        const width = boundingRect.width;
        const height = boundingRect.height;
        
        // 提取组件图片
        const componentRegion = image.getRegion(boundingRect);
        
        // 确定组件类型
        const type = this.determineComponentType(componentRegion, width, height);
        
        // 生成唯一ID
        const id = `component-${type}-${index}`;
        
        return {
          id,
          type,
          x,
          y,
          width,
          height,
          contour: contour.getPoints()
        };
      });
      
      // 8. 检测背景区域
      const background = this.detectBackground(image, components);
      
      // 9. 添加背景到组件列表
      const allComponents = [background, ...components];
      
      // 10. 将结果保存到JSON文件 (用于调试)
      const debugOutputPath = path.join(path.dirname(imagePath), 'component-detection-result.json');
      fs.writeFileSync(
        debugOutputPath,
        JSON.stringify(allComponents, null, 2)
      );
      
      console.log(`组件检测完成，总共 ${allComponents.length} 个组件`);
      return allComponents;
      
    } catch (error) {
      console.error('组件检测失败:', error);
      throw error;
    }
  }
  
  /**
   * 确定组件类型
   * @param {Mat} componentImage - 组件图像
   * @param {number} width - 组件宽度
   * @param {number} height - 组件高度
   * @returns {string} 组件类型
   */
  determineComponentType(componentImage, width, height) {
    // 基于尺寸比例和图像特征进行简单分类
    const aspectRatio = width / height;
    
    if (width > 500 && height > 500) {
      return 'panel';
    } else if (aspectRatio > 4) {
      return 'header';
    } else if (aspectRatio < 0.25) {
      return 'sidebar';
    } else if (width < 50 && height < 50) {
      return 'icon';
    } else if (width < 200 && height < 100) {
      return 'button';
    } else if (width > 300 && height > 200) {
      return 'chart';
    } else {
      return 'unknown';
    }
  }
  
  /**
   * 检测背景区域
   * @param {Mat} image - 原始图像
   * @param {Array} components - 已检测的组件列表
   * @returns {Object} 背景组件
   */
  detectBackground(image, components) {
    const sizes = image.sizes;
    return {
      id: 'background',
      type: 'background',
      x: 0,
      y: 0,
      width: sizes[1], // 宽
      height: sizes[0], // 高
      isBackground: true
    };
  }
  
  /**
   * 可视化检测结果
   * @param {string} imagePath - 原始图片路径
   * @param {Array} components - 检测到的组件
   * @param {string} outputPath - 输出图片路径
   */
  async visualizeComponents(imagePath, components, outputPath) {
    try {
      const image = await cv.imreadAsync(imagePath);
      
      // 为每个组件绘制边界框
      components.forEach(component => {
        if (component.type === 'background') return;
        
        const { x, y, width, height, type } = component;
        const color = this.getColorForType(type);
        const thickness = 2;
        
        // 绘制矩形
        image.drawRectangle(
          new cv.Rect(x, y, width, height),
          color,
          thickness
        );
        
        // 添加标签
        image.putText(
          `${type}`,
          new cv.Point2(x, y - 5),
          cv.FONT_HERSHEY_SIMPLEX,
          0.5,
          color,
          1
        );
      });
      
      // 保存结果
      await cv.imwriteAsync(outputPath, image);
      console.log(`可视化结果保存至: ${outputPath}`);
      
    } catch (error) {
      console.error('可视化组件失败:', error);
    }
  }
  
  /**
   * 根据组件类型获取颜色
   * @param {string} type - 组件类型
   * @returns {Vec3} BGR颜色
   */
  getColorForType(type) {
    const colors = {
      panel: new cv.Vec3(0, 255, 0),     // 绿色
      header: new cv.Vec3(255, 0, 0),    // 蓝色
      sidebar: new cv.Vec3(0, 0, 255),   // 红色
      button: new cv.Vec3(255, 255, 0),  // 青色
      icon: new cv.Vec3(255, 0, 255),    // 洋红色
      chart: new cv.Vec3(0, 255, 255),   // 黄色
      unknown: new cv.Vec3(128, 128, 128) // 灰色
    };
    
    return colors[type] || colors.unknown;
  }
}

module.exports = AdvancedComponentDetector; 