#!/usr/bin/env node

/**
 * RAGFlow API 转换工具命令行接口
 * 
 * 使用方法:
 * node convert.js                                    # 使用默认路径
 * node convert.js input.md output.json               # 指定输入输出文件
 * node convert.js --help                             # 显示帮助信息
 * node convert.js --validate                         # 仅验证现有的OpenAPI文件
 * node convert.js --incremental                      # 增量更新模式
 */

const fs = require('fs');
const MarkdownToOpenAPIConverter = require('./auto-convert-api');

class ConvertCLI {
  constructor() {
    this.args = process.argv.slice(2);
    this.options = this.parseArgs();
  }

  parseArgs() {
    const options = {
      input: 'http_api_reference.md',
      output: 'public/openapi.json',
      help: false,
      validate: false,
      incremental: false,
      config: 'tools/converter-config.json'
    };

    for (let i = 0; i < this.args.length; i++) {
      const arg = this.args[i];
      
      switch (arg) {
        case '--help':
        case '-h':
          options.help = true;
          break;
        case '--validate':
        case '-v':
          options.validate = true;
          break;
        case '--incremental':
        case '-i':
          options.incremental = true;
          break;
        case '--config':
        case '-c':
          options.config = this.args[++i];
          break;
        default:
          if (!arg.startsWith('-')) {
            if (!options.inputSet) {
              options.input = arg;
              options.inputSet = true;
            } else if (!options.outputSet) {
              options.output = arg;
              options.outputSet = true;
            }
          }
          break;
      }
    }

    return options;
  }

  showHelp() {
    console.log(`
🔄 RAGFlow API Markdown to OpenAPI 转换工具

使用方法:
  node convert.js [选项] [输入文件] [输出文件]

选项:
  -h, --help          显示此帮助信息
  -v, --validate      仅验证现有的OpenAPI文件
  -i, --incremental   增量更新模式（保留现有的自定义修改）
  -c, --config FILE   指定配置文件路径 (默认: converter-config.json)

参数:
  输入文件            Markdown API文档路径 (默认: http_api_reference.md)
  输出文件            OpenAPI JSON输出路径 (默认: public/openapi.json)

示例:
  node convert.js                                    # 使用默认设置
  node convert.js api.md openapi.json                # 指定输入输出文件
  node convert.js --validate                         # 仅验证现有文件
  node convert.js --incremental                      # 增量更新
  node convert.js --config my-config.json            # 使用自定义配置

验证工具:
  npm run analyze                                    # 分析转换结果
  npm run check-responses                            # 检查响应模式
  npm run validate-all                               # 运行所有验证
`);
  }

  async validateOnly() {
    console.log('🔍 验证模式：检查现有OpenAPI文件');
    
    if (!fs.existsSync(this.options.output)) {
      console.error(`❌ 文件不存在: ${this.options.output}`);
      return false;
    }

    try {
      const content = fs.readFileSync(this.options.output, 'utf8');
      const openapi = JSON.parse(content);
      
      console.log('✅ JSON格式有效');
      console.log(`📊 基本信息:`);
      console.log(`   - OpenAPI版本: ${openapi.openapi}`);
      console.log(`   - API标题: ${openapi.info?.title}`);
      console.log(`   - API版本: ${openapi.info?.version}`);
      console.log(`   - 路径数量: ${Object.keys(openapi.paths || {}).length}`);
      
      let methodCount = 0;
      Object.values(openapi.paths || {}).forEach(pathObj => {
        methodCount += Object.keys(pathObj).length;
      });
      console.log(`   - 方法数量: ${methodCount}`);
      
      // 运行详细验证
      console.log('\n🔍 运行详细验证...');
      const { spawn } = require('child_process');
      
      return new Promise((resolve) => {
        const child = spawn('node', ['analyze-api.js'], { stdio: 'inherit' });
        child.on('close', (code) => {
          resolve(code === 0);
        });
      });
      
    } catch (error) {
      console.error(`❌ 验证失败: ${error.message}`);
      return false;
    }
  }

  async incrementalUpdate() {
    console.log('🔄 增量更新模式：保留现有自定义修改');
    
    let existingSpec = null;
    if (fs.existsSync(this.options.output)) {
      try {
        const content = fs.readFileSync(this.options.output, 'utf8');
        existingSpec = JSON.parse(content);
        console.log('📋 已加载现有OpenAPI规范');
      } catch (error) {
        console.warn(`⚠️  无法解析现有文件，将进行完整转换: ${error.message}`);
      }
    }

    // 执行转换
    const converter = new MarkdownToOpenAPIConverter(this.options.config);
    const result = await converter.convert(this.options.input, this.options.output);
    
    if (result.success && existingSpec) {
      // 合并自定义修改
      console.log('🔀 合并自定义修改...');
      this.mergeCustomizations(converter.openApiSpec, existingSpec);
      
      // 重新保存
      converter.saveOpenApiSpec(this.options.output);
    }
    
    return result;
  }

  mergeCustomizations(newSpec, existingSpec) {
    // 保留自定义的服务器配置
    if (existingSpec.servers && existingSpec.servers.length > newSpec.servers.length) {
      console.log('   - 保留自定义服务器配置');
      newSpec.servers = existingSpec.servers;
    }

    // 保留自定义的组件
    if (existingSpec.components) {
      if (existingSpec.components.schemas && !newSpec.components.schemas) {
        console.log('   - 保留自定义模式定义');
        newSpec.components.schemas = existingSpec.components.schemas;
      }
    }

    // 保留路径级别的自定义修改
    Object.keys(existingSpec.paths || {}).forEach(path => {
      if (newSpec.paths[path]) {
        Object.keys(existingSpec.paths[path]).forEach(method => {
          if (newSpec.paths[path][method]) {
            const existingOp = existingSpec.paths[path][method];
            const newOp = newSpec.paths[path][method];
            
            // 保留自定义的标签
            if (existingOp.tags && !newOp.tags) {
              newOp.tags = existingOp.tags;
            }
            
            // 保留自定义的示例
            if (existingOp.examples && !newOp.examples) {
              newOp.examples = existingOp.examples;
            }
          }
        });
      }
    });
  }

  async run() {
    console.log('🔄 RAGFlow API Markdown to OpenAPI 转换工具');
    console.log('================================================');

    if (this.options.help) {
      this.showHelp();
      return;
    }

    if (this.options.validate) {
      const success = await this.validateOnly();
      process.exit(success ? 0 : 1);
      return;
    }

    // 检查输入文件
    if (!fs.existsSync(this.options.input)) {
      console.error(`❌ 输入文件不存在: ${this.options.input}`);
      process.exit(1);
      return;
    }

    console.log(`📖 输入文件: ${this.options.input}`);
    console.log(`💾 输出文件: ${this.options.output}`);
    console.log(`⚙️  配置文件: ${this.options.config}`);
    console.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);

    try {
      let result;
      
      if (this.options.incremental) {
        result = await this.incrementalUpdate();
      } else {
        const converter = new MarkdownToOpenAPIConverter(this.options.config);
        result = await converter.convert(this.options.input, this.options.output);
      }

      if (result.success) {
        console.log('\n🎉 转换成功完成！');
        console.log(`⏰ 完成时间: ${new Date().toLocaleString()}`);
        console.log(`📊 转换统计: ${result.pathCount} 个路径`);
        
        if (result.validation.warnings.length > 0) {
          console.log(`⚠️  ${result.validation.warnings.length} 个警告`);
        }
        
        console.log('\n📋 建议的下一步操作:');
        console.log('   1. 验证转换结果: node analyze-api.js');
        console.log('   2. 检查响应模式: node check-responses.js');
        console.log('   3. 启动服务器测试: npm start');
        console.log('   4. 在浏览器中查看: http://localhost:3000');
        
      } else {
        console.error(`❌ 转换失败: ${result.error}`);
        process.exit(1);
      }
      
    } catch (error) {
      console.error(`❌ 转换过程中发生错误: ${error.message}`);
      console.error(error.stack);
      process.exit(1);
    }
  }
}

// 运行CLI
if (require.main === module) {
  const cli = new ConvertCLI();
  cli.run().catch(error => {
    console.error(`❌ 程序异常: ${error.message}`);
    process.exit(1);
  });
}

module.exports = ConvertCLI;
