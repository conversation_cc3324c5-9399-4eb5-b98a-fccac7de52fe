const fs = require('fs');
const path = require('path');
const https = require('https');

const swaggerUIFiles = [
  {
    url: 'https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui.css',
    destination: path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui.css')
  },
  {
    url: 'https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui-bundle.js',
    destination: path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-bundle.js')
  },
  {
    url: 'https://unpkg.com/swagger-ui-dist@5.10.1/swagger-ui-standalone-preset.js',
    destination: path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-standalone-preset.js')
  }
];

// 确保目录存在
if (!fs.existsSync(path.join(__dirname, 'public', 'swagger-ui'))) {
  fs.mkdirSync(path.join(__dirname, 'public', 'swagger-ui'), { recursive: true });
}

// 下载文件
function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    console.log(`正在下载 ${url} 到 ${destination}...`);
    
    const file = fs.createWriteStream(destination);
    https.get(url, (response) => {
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`已下载 ${destination}`);
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(destination, () => {}); // 删除部分下载的文件
      console.error(`下载 ${url} 失败: ${err.message}`);
      reject(err);
    });
  });
}

// 依次下载所有文件
async function downloadAllFiles() {
  for (const file of swaggerUIFiles) {
    try {
      await downloadFile(file.url, file.destination);
    } catch (err) {
      console.error(`下载失败: ${err}`);
    }
  }
  console.log('所有文件下载完成！');
}

downloadAllFiles(); 