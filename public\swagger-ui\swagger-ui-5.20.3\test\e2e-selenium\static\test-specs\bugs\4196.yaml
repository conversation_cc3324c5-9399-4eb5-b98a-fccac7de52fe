openapi: 3.0.4
info:
  title: Demo API
  description: First test
  termsOfService: 'http://demo.io/terms-of-service/'
  contact:
    name: Demo Support
    email: <EMAIL>
  version: 1.0.0
servers:
  - url: '{server}/v1'
    variables:
      server:
        default: https://api.demo.io
        description: the API endpoint

paths:
  /session:
    put:
      summary: Returns a new authentication token
      tags:
        - session
      security:
        - basicAuth: []
      responses:
        '201':
          description: A session object
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    properties:
                      user_id:
                        type: string
                        format: uuid
                        readOnly: true
                        example: 110e8400-e29b-11d4-a716-************
                  - $ref: '#/components/schemas/Session'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Session:
      required:
        - token
      properties:
        token:
          type: string
          readOnly: true
          example: >-
            *************************************************************************************************************************************************************************************************************************************************************************************

    Error:
      required:
        - message
      properties:
        message:
          description: a human readable message explaining the error
          type: string
        reason:
          description: a functional key about the error
          type: string
  responses:
    Unauthorized:
      description: Not authenticated
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Default:
      description: unexpected error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
