<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RAGFlow API 文档</title>
  <link rel="stylesheet" href="./swagger-ui/swagger-ui.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }
    .swagger-ui .topbar {
      background-color: #1a365d;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>

  <script src="./swagger-ui/swagger-ui-bundle.js"></script>
  <script src="./swagger-ui/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: './openapi.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        layout: "StandaloneLayout",
        requestInterceptor: (request) => {
          // 可以在这里添加API密钥等请求拦截
          return request;
        }
      });
      window.ui = ui;
    };
  </script>
</body>
</html> 