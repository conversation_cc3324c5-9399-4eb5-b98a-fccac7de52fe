describe("#6158: read-only property is not hidden in `POST/PUT`", () => {
  describe("POST", () => {
    it("should hide property 'id'", () => {
      cy.visit("/?url=/documents/bugs/6158.yaml")
        .get("#operations-User-post_users")
        .click()
        .get(".parameters[data-property-name='id']")
        .should("not.exist")
        .get(".parameters[data-property-name='name']")
        .should("exist")
    })
    it("should hide property 'id' when trying it out", () => {
      cy.visit("/?url=/documents/bugs/6158.yaml")
        .get("#operations-User-post_users")
        .click()
        .get(".try-out__btn")
        .click()
        .get(".parameters[data-property-name='id']")
        .should("not.exist")
        .get("input[placeholder='id']")
        .should("not.exist")
        .get(".parameters[data-property-name='name']")
        .should("exist")
        .get("input[placeholder='name']")
        .should("exist")
    })
  })
  describe("PUT", () => {
    it("should hide property 'id'", () => {
      cy.visit("/?url=/documents/bugs/6158.yaml")
        .get("#operations-User-put_users")
        .click()
        .get(".parameters[data-property-name='id']")
        .should("not.exist")
        .get(".parameters[data-property-name='name']")
        .should("exist")
    })
    it("should hide property 'id' when trying it out", () => {
      cy.visit("/?url=/documents/bugs/6158.yaml")
        .get("#operations-User-put_users")
        .click()
        .get(".try-out__btn")
        .click()
        .get(".parameters[data-property-name='id']")
        .should("not.exist")
        .get("input[placeholder='id']")
        .should("not.exist")
        .get(".parameters[data-property-name='name']")
        .should("exist")
        .get("input[placeholder='name']")
        .should("exist")
    })
  })
})
