const fs = require('fs');

// 读取Markdown文档
const md = fs.readFileSync('http_api_reference.md', 'utf8');

// 读取OpenAPI JSON
const openapi = JSON.parse(fs.readFileSync('public/openapi.json', 'utf8'));

// 分析Markdown中的API端点
console.log('=== Markdown API分析 ===');
const lines = md.split('\n');
const apiEndpoints = [];

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  if (line.match(/^\*\*(POST|GET|PUT|DELETE)\*\*/)) {
    const methodMatch = line.match(/^\*\*(POST|GET|PUT|DELETE)\*\*/);
    const pathMatch = line.match(/`([^`]+)`/);
    if (methodMatch && pathMatch) {
      apiEndpoints.push({
        method: methodMatch[1],
        path: pathMatch[1],
        line: i + 1
      });
    }
  }
}

console.log(`Markdown中发现 ${apiEndpoints.length} 个API端点:`);
apiEndpoints.forEach((endpoint, i) => {
  console.log(`${i + 1}. ${endpoint.method} ${endpoint.path} (行 ${endpoint.line})`);
});

// 分析OpenAPI JSON中的路径
console.log('\n=== OpenAPI JSON分析 ===');
const openapiPaths = Object.keys(openapi.paths);
console.log(`OpenAPI JSON中有 ${openapiPaths.length} 个路径:`);

let totalMethods = 0;
openapiPaths.forEach((path, i) => {
  const methods = Object.keys(openapi.paths[path]);
  totalMethods += methods.length;
  console.log(`${i + 1}. ${path} [${methods.join(', ').toUpperCase()}]`);
});

console.log(`\n总计 ${totalMethods} 个方法`);

// 对比分析
console.log('\n=== 转换完整性分析 ===');
console.log(`Markdown API端点: ${apiEndpoints.length}`);
console.log(`OpenAPI 方法总数: ${totalMethods}`);

if (apiEndpoints.length === totalMethods) {
  console.log('✅ API端点数量匹配');
} else {
  console.log('❌ API端点数量不匹配');
  console.log(`差异: ${Math.abs(apiEndpoints.length - totalMethods)} 个端点`);
}

// 检查OpenAPI规范的结构
console.log('\n=== OpenAPI规范结构验证 ===');
console.log(`OpenAPI版本: ${openapi.openapi}`);
console.log(`API标题: ${openapi.info.title}`);
console.log(`API版本: ${openapi.info.version}`);
console.log(`服务器数量: ${openapi.servers ? openapi.servers.length : 0}`);
console.log(`安全方案数量: ${openapi.components && openapi.components.securitySchemes ? Object.keys(openapi.components.securitySchemes).length : 0}`);
console.log(`参数组件数量: ${openapi.components && openapi.components.parameters ? Object.keys(openapi.components.parameters).length : 0}`);

// 验证必需字段
const requiredFields = ['openapi', 'info', 'paths'];
const missingFields = requiredFields.filter(field => !openapi[field]);

if (missingFields.length === 0) {
  console.log('✅ 所有必需字段都存在');
} else {
  console.log('❌ 缺少必需字段:', missingFields.join(', '));
}

// 检查路径定义的完整性
console.log('\n=== 路径定义完整性检查 ===');
let pathsWithIssues = 0;

openapiPaths.forEach(path => {
  const pathObj = openapi.paths[path];
  Object.keys(pathObj).forEach(method => {
    const operation = pathObj[method];
    const issues = [];
    
    if (!operation.summary) issues.push('缺少summary');
    if (!operation.description) issues.push('缺少description');
    if (!operation.operationId) issues.push('缺少operationId');
    if (!operation.responses) issues.push('缺少responses');
    
    if (issues.length > 0) {
      console.log(`⚠️  ${method.toUpperCase()} ${path}: ${issues.join(', ')}`);
      pathsWithIssues++;
    }
  });
});

if (pathsWithIssues === 0) {
  console.log('✅ 所有路径定义都完整');
} else {
  console.log(`❌ ${pathsWithIssues} 个操作存在问题`);
}
