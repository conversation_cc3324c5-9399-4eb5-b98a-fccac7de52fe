const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const PORT = process.env.PORT || 3000;

// 检查Swagger UI文件是否存在
const swaggerUICss = path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui.css');
const swaggerUIBundle = path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-bundle.js');
const swaggerUIStandalone = path.join(__dirname, 'public', 'swagger-ui', 'swagger-ui-standalone-preset.js');

const swaggerUIFilesExist = 
  fs.existsSync(swaggerUICss) && 
  fs.existsSync(swaggerUIBundle) && 
  fs.existsSync(swaggerUIStandalone);

// 提供静态文件
app.use(express.static(path.join(__dirname, 'public')));

// 处理根路径请求
app.get('/', (req, res) => {
  if (swaggerUIFilesExist) {
    // 如果Swagger UI文件存在，使用常规HTML
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
  } else {
    // 如果Swagger UI文件不存在，显示一个简单的替代页面，直接显示API定义
    res.send(`
<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RAGFlow API 文档 (离线简易版)</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow: auto;
      max-height: 80vh;
    }
    .warning {
      background-color: #fff3cd;
      color: #856404;
      padding: 10px 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1 {
      color: #1a365d;
    }
  </style>
</head>
<body>
  <div class="warning">
    <strong>注意：</strong> Swagger UI资源文件未找到。正在显示简易版API文档。请按照
    <a href="swagger-ui/manual-install.txt">手动安装说明</a>下载必要的文件。
  </div>

  <h1>RAGFlow API 文档 (离线简易版)</h1>
  <p>以下是API的OpenAPI JSON定义。要获得更好的体验，请安装Swagger UI文件。</p>
  
  <pre id="json-container">加载中...</pre>

  <script>
    fetch('./openapi.json')
      .then(response => response.json())
      .then(data => {
        document.getElementById('json-container').textContent = JSON.stringify(data, null, 2);
      })
      .catch(err => {
        document.getElementById('json-container').textContent = '无法加载API定义: ' + err.message;
      });
  </script>
</body>
</html>
    `);
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  if (!swaggerUIFilesExist) {
    console.log('警告: 未找到Swagger UI文件，将使用简易模式显示API定义');
    console.log('请参考 public/swagger-ui/manual-install.txt 手动安装Swagger UI文件');
  }
}); 