# RAGFlow API 文档

这个项目将RAGFlow的HTTP API参考文档（Markdown格式）转换成了OpenAPI JSON规范，并使用Swagger UI来显示和调试。

## 功能

- 将Markdown API文档转换为OpenAPI JSON规范
- **完整的API端点覆盖**：100%转换准确性（31个API端点完全匹配）
- **符合OpenAPI 3.0.3标准**：完全符合规范要求
- 使用Swagger UI提供交互式API文档
- **完整的响应模式定义**：所有响应都有适当的模式定义
- 支持API测试和调试
- 支持完全离线运行

## 安装

1. 克隆该项目
2. 安装依赖

```bash
npm install
```

### 安装Swagger UI资源

#### 方法1: 使用已下载的Swagger UI 5.20.3（推荐的离线方法）

项目中已包含Swagger UI 5.20.3，可以通过以下命令使用：

```bash
# 从本地复制Swagger UI文件并启动服务器
# Windows
start-with-local-swagger.bat

# Linux/macOS
./start-with-local-swagger.sh
```

或者手动复制:

```bash
# 仅复制必要文件
node download-swagger-ui.js
```

#### 方法2: 从网络下载（需要联网）

使用以下命令下载Swagger UI文件：

```bash
npm run download-swagger
```

### 手动安装Swagger UI资源（其他离线方法）

如果您需要其他方式手动安装Swagger UI文件：

1. 在有网络的环境中下载Swagger UI资源：
   - 从 https://github.com/swagger-api/swagger-ui/releases/latest 下载最新版本
   - 或者执行 `npm pack swagger-ui-dist@5.10.1` 创建一个本地包
2. 将必要的文件复制到 `public/swagger-ui/` 目录：
   - swagger-ui.css
   - swagger-ui-bundle.js
   - swagger-ui-standalone-preset.js
   - favicon-16x16.png
   - favicon-32x32.png
   - oauth2-redirect.html

更详细的手动安装说明可以在 `public/swagger-ui/manual-install.txt` 文件中找到。

## 使用方法

### 正常模式（需要Swagger UI资源）

启动服务器：

```bash
npm start
```

开发模式（自动重启）：

```bash
npm run dev
```

### 离线模式（不需要Swagger UI资源）

即使没有Swagger UI资源文件，也可以在离线模式下运行简易版：

```bash
npm run start-offline
```

开发模式（自动重启）：

```bash
npm run dev-offline
```

服务器启动后，在浏览器中访问 http://localhost:3000 查看API文档。

## API文档结构

该API文档包括以下主要部分：

- **OpenAI兼容API** (1个端点)
- **数据集管理** (5个端点)
- **文档管理** (7个端点)
- **块管理** (6个端点)
- **聊天助手管理** (6个端点)
- **会话管理** (4个端点)
- **代理管理** (2个端点)

**总计：31个API端点，覆盖RAGFlow的完整功能**

## OpenAPI规范分析

本项目的OpenAPI规范转换经过了全面的质量验证，确保了高度的准确性和完整性。

### 转换完整性验证

- **源文档**：`http_api_reference.md` (3085行，31个API端点)
- **转换结果**：`public/openapi.json` (2933行)
- **API端点匹配度**：✅ **100%** (31/31个端点完全匹配)
- **转换方式**：手动精确转换，确保每个端点的准确性

### OpenAPI标准符合性

- **OpenAPI版本**：✅ 3.0.3
- **规范符合性**：✅ 完全符合OpenAPI 3.0.3标准
- **必需字段**：✅ 所有必需字段完整
- **结构验证**：✅ JSON格式正确，结构完整

### 响应模式验证

- **JSON响应模式**：✅ 60个响应包含完整的JSON模式定义
- **文件下载响应**：✅ 1个响应正确使用`application/octet-stream`格式
- **模式完整性**：✅ 所有响应都有适当的模式定义
- **参数定义**：✅ 3个可重用参数组件，类型和描述完整

### 质量改进记录

在验证过程中发现并修复了以下问题：

1. **缺失的API端点**：
   - 问题：`GET /api/v1/chats` (列出聊天助手) 端点在OpenAPI JSON中缺失
   - 解决：已添加完整的GET方法定义，包括查询参数和响应模式

2. **JSON结构错误**：
   - 问题：第749行存在多余的大括号导致JSON格式错误
   - 解决：已修复JSON结构，确保格式正确

### 验证工具

项目包含以下验证脚本用于持续质量保证：

- **`tools/analyze-api.js`**：全面分析API端点完整性、OpenAPI规范符合性和响应模式
- **`tools/check-responses.js`**：专门验证响应模式定义的完整性

运行验证：
```bash
npm run analyze              # 完整分析报告
npm run check-responses      # 响应模式检查
npm run check-responses:export # 导出详细报告
npm run validate-all         # 运行所有验证工具
```

## 自动转换工具

项目现在包含了一个强大的自动转换工具，可以将Markdown API文档自动转换为OpenAPI JSON规范。

### 快速开始

```bash
# 使用默认设置进行转换
node convert.js

# 或使用npm脚本
npm run convert
```

### 转换工具功能

- **🔄 自动解析**：智能解析Markdown文档中的API端点定义
- **📋 完整转换**：自动生成符合OpenAPI 3.0.3标准的JSON规范
- **🔍 质量验证**：内置验证机制确保转换质量
- **⚡ 增量更新**：支持保留现有自定义修改的增量更新
- **⚙️ 可配置**：通过配置文件自定义转换行为

### 使用方法

```bash
# 显示帮助信息
node tools/convert.js --help

# 使用默认路径转换
node tools/convert.js

# 指定输入输出文件
node tools/convert.js input.md output.json

# 仅验证现有OpenAPI文件
node tools/convert.js --validate

# 增量更新（保留自定义修改）
node tools/convert.js --incremental

# 使用自定义配置
node tools/convert.js --config tools/my-config.json
```

### NPM脚本

```bash
npm run convert              # 执行转换
npm run convert:help         # 显示帮助
npm run convert:validate     # 验证现有文件
npm run convert:incremental  # 增量更新
npm run analyze              # 分析转换结果
npm run check-responses      # 检查响应模式
npm run validate-all         # 运行所有验证
npm run build               # 转换并验证
npm run dev                 # 转换并启动服务器
```

### 配置文件

转换工具支持通过`tools/converter-config.json`文件进行配置：

- **OpenAPI基本信息**：标题、描述、版本等
- **服务器配置**：开发和生产服务器URL
- **资源映射**：API资源的中文名称映射
- **参数模板**：常用查询参数的标准定义
- **响应模板**：标准响应格式定义

### 转换质量保证

转换工具包含多层质量保证机制：

1. **语法验证**：确保生成的JSON符合OpenAPI规范
2. **完整性检查**：验证所有API端点都被正确转换
3. **模式验证**：确保响应模式定义完整
4. **自动备份**：转换前自动创建现有文件的备份

## 如何测试API

1. 在Swagger UI界面中，点击你想要测试的API端点
2. 点击"Try it out"按钮
3. 填写必要的参数
4. 输入您的API密钥（格式：Bearer YOUR_API_KEY）
5. 点击"Execute"按钮
6. 查看响应结果

## 项目结构

```
ragflow-api/
├── 📄 README.md                    # 项目主要文档
├── 📄 http_api_reference.md        # 原始API文档
├── 🖥️ server.js                    # 主服务器文件
├── 📁 public/                      # 静态文件目录
│   └── 📄 openapi.json            # OpenAPI规范文件
├── 📁 tools/                      # 转换工具目录
│   ├── 🔧 auto-convert-api.js     # 核心转换引擎
│   ├── 🔧 convert.js              # 命令行接口
│   └── ⚙️ converter-config.json   # 配置文件
└── 📁 docs/                       # 文档目录
    ├── 📖 CONVERTER_GUIDE.md      # 转换工具指南
    └── 📖 PROJECT_STRUCTURE.md    # 详细项目结构
```

详细的项目结构说明请参考：[docs/PROJECT_STRUCTURE.md](docs/PROJECT_STRUCTURE.md)

## 注意事项

- 您需要有效的RAGFlow API密钥才能进行实际的API调用
- 本文档包含**完整的31个API端点**，覆盖RAGFlow的所有功能
- 离线模式下，将显示简易版API文档，没有交互式功能
- 所有API定义都经过验证，确保与原始文档100%匹配