PS C:\AI\api> wsl -d Ubuntu-24.04 -- bash -c "echo '=== 所有已安装的apt包 ===' && dpkg -l | grep '^ii' | awk '{print $2}' | sort"
wsl: 检测到 localhost 代理配置，但未镜像到 WSL。NAT 模式下的 WSL 不支持 localhost 代理。
=== 所有已安装的apt包 ===
ii  adduser                         3.137ubuntu1                            all          add and remove users and groups
ii  adwaita-icon-theme              46.0-1                                  all          default icon theme of GNOME
ii  apparmor                        4.0.1really4.0.1-0ubuntu0.24.04.4       amd64        user-space parser utility for AppArmor
ii  apport                          2.28.1-0ubuntu3.8                       all          automatically generate crash reports for debugging
ii  apport-core-dump-handler        2.28.1-0ubuntu3.8                       all          Kernel core dump handler for Apport
ii  apport-symptoms                 0.25                                    all          symptom scripts for apport
ii  appstream                       1.0.2-1build6                           amd64        Software component metadata management
ii  apt                             2.8.3                                   amd64        commandline package manager
ii  apt-utils                       2.8.3                                   amd64        package management related utility programs
ii  at-spi2-common                  2.52.0-1build1                          all          Assistive Technology Service Provider Interface (common files)
ii  at-spi2-core                    2.52.0-1build1                          amd64        Assistive Technology Service Provider Interface (D-Bus core)
ii  base-files                      13ubuntu10.2                            amd64        Debian base system miscellaneous files
ii  base-passwd                     3.6.3build1                             amd64        Debian base system master password and group files
ii  bash                            5.2.21-2ubuntu4                         amd64        GNU Bourne Again SHell
ii  bash-completion                 1:2.11-8                                all          programmable completion for the bash shell
ii  bc                              1.07.1-3ubuntu4                         amd64        GNU bc arbitrary precision calculator language
ii  binutils                        2.42-4ubuntu2.5                         amd64        GNU assembler, linker and binary utilities
ii  binutils-common:amd64           2.42-4ubuntu2.5                         amd64        Common files for the GNU assembler, linker and binary utilities
ii  binutils-x86-64-linux-gnu       2.42-4ubuntu2.5                         amd64        GNU binary utilities, for x86-64-linux-gnu target
ii  bsdextrautils                   2.39.3-9ubuntu6.2                       amd64        extra utilities from 4.4BSD-Lite
ii  bsdutils                        1:2.39.3-9ubuntu6.2                     amd64        basic utilities from 4.4BSD-Lite
ii  build-essential                 12.10ubuntu1                            amd64        Informational list of build-essential packages
ii  byobu                           6.11-0ubuntu1                           all          text window manager, shell multiplexer, integrated DevOps environment
ii  bzip2                           1.0.8-5.1build0.1                       amd64        high-quality block-sorting file compressor - utilities
ii  ca-certificates                 20240203                                all          Common CA certificates
ii  catimg                          2.7.0-2                                 amd64        fast image printing in to your terminal
ii  cloud-guest-utils               0.33-1                                  all          cloud guest utilities
ii  cloud-init                      25.1.4-0ubuntu0~24.04.1                 all          initialization and customization tool for cloud instances
ii  command-not-found               23.04.0                                 all          Suggest installation of packages in interactive bash sessions
ii  console-setup                   1.226ubuntu1                            all          console font and keymap setup program
ii  console-setup-linux             1.226ubuntu1                            all          Linux specific part of console-setup
ii  coreutils                       9.4-3ubuntu6                            amd64        GNU core utilities
ii  cpp                             4:13.2.0-7ubuntu1                       amd64        GNU C preprocessor (cpp)
ii  cpp-13                          13.3.0-6ubuntu2~24.04                   amd64        GNU C preprocessor
ii  cpp-13-x86-64-linux-gnu         13.3.0-6ubuntu2~24.04                   amd64        GNU C preprocessor for x86_64-linux-gnu
ii  cpp-x86-64-linux-gnu            4:13.2.0-7ubuntu1                       amd64        GNU C preprocessor (cpp) for the amd64 architecture
ii  cron                            3.0pl1-184ubuntu2                       amd64        process scheduling daemon
ii  cron-daemon-common              3.0pl1-184ubuntu2                       all          process scheduling daemon's configuration files
ii  curl                            8.5.0-2ubuntu10.6                       amd64        command line tool for transferring data with URL syntax
ii  dash                            0.5.12-6ubuntu5                         amd64        POSIX-compliant shell
ii  dbus                            1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (system message bus)
ii  dbus-bin                        1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (command line utilities)
ii  dbus-daemon                     1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (reference message bus)
ii  dbus-session-bus-common         1.14.10-4ubuntu4.1                      all          simple interprocess messaging system (session bus configuration)
ii  dbus-system-bus-common          1.14.10-4ubuntu4.1                      all          simple interprocess messaging system (system bus configuration)
ii  dbus-user-session               1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (systemd --user integration)
ii  dbus-x11                        1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (X11 deps)
ii  dconf-gsettings-backend:amd64   0.40.0-4build2                          amd64        simple configuration storage system - GSettings back-end
ii  dconf-service                   0.40.0-4build2                          amd64        simple configuration storage system - D-Bus service
ii  debconf                         1.5.86ubuntu1                           all          Debian configuration management system
ii  debconf-i18n                    1.5.86ubuntu1                           all          full internationalization support for debconf
ii  debianutils                     5.17build1                              amd64        Miscellaneous utilities specific to Debian
ii  dhcpcd-base                     1:10.0.6-1ubuntu3.1                     amd64        DHCPv4 and DHCPv6 dual-stack client (binaries and exit hooks)
ii  diffutils                       1:3.10-1build1                          amd64        File comparison utilities
ii  dirmngr                         2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - network certificate management service
ii  distro-info                     1.7build1                               amd64        provides information about the distributions' releases
ii  distro-info-data                0.60ubuntu0.3                           all          information about the distributions' releases (data files)
ii  dmsetup                         2:1.02.185-3ubuntu3.2                   amd64        Linux Kernel Device Mapper userspace library
ii  dpkg                            1.22.6ubuntu6.1                         amd64        Debian package management system
ii  dpkg-dev                        1.22.6ubuntu6.1                         all          Debian package development tools
ii  e2fsprogs                       1.47.0-2.4~exp1ubuntu4.1                amd64        ext2/ext3/ext4 file system utilities
ii  e2fsprogs-l10n                  1.47.0-2.4~exp1ubuntu4.1                all          ext2/ext3/ext4 file system utilities - translations
ii  eatmydata                       131-1ubuntu1                            all          Library and utilities designed to disable fsync and friends
ii  ed                              1.20.1-1                                amd64        classic UNIX line editor
ii  eject                           2.39.3-9ubuntu6.2                       amd64        ejects CDs and operates CD-Changers under Linux
ii  ethtool                         1:6.7-1build1                           amd64        display or change Ethernet device settings
ii  eza                             0.18.2-1                                amd64        Modern replacement for ls
ii  fakeroot                        1.33-1                                  amd64        tool for simulating superuser privileges
ii  fdisk                           2.39.3-9ubuntu6.2                       amd64        collection of partitioning utilities
ii  file                            1:5.45-3build1                          amd64        Recognize the type of data in a file using "magic" numbers
ii  findutils                       4.9.0-5build1                           amd64        utilities for finding files--find, xargs
ii  fontconfig                      2.15.0-1.1ubuntu2                       amd64        generic font configuration library - support binaries
ii  fontconfig-config               2.15.0-1.1ubuntu2                       amd64        generic font configuration library - configuration
ii  fonts-dejavu-core               2.37-8                                  all          Vera font family derivate with additional characters
ii  fonts-dejavu-mono               2.37-8                                  all          Vera font family derivate with additional characters
ii  fonts-lato                      2.015-1                                 all          sans-serif typeface family font
ii  fonts-ubuntu                    0.869+git20240321-0ubuntu1              all          sans-serif font set from Ubuntu
ii  fuse3                           3.14.0-5build1                          amd64        Filesystem in Userspace (3.x version)
ii  g++                             4:13.2.0-7ubuntu1                       amd64        GNU C++ compiler
ii  g++-13                          13.3.0-6ubuntu2~24.04                   amd64        GNU C++ compiler
ii  g++-13-x86-64-linux-gnu         13.3.0-6ubuntu2~24.04                   amd64        GNU C++ compiler for x86_64-linux-gnu architecture
ii  g++-x86-64-linux-gnu            4:13.2.0-7ubuntu1                       amd64        GNU C++ compiler for the amd64 architecture
ii  gawk                            1:5.2.1-2build3                         amd64        GNU awk, a pattern scanning and processing language
ii  gcc                             4:13.2.0-7ubuntu1                       amd64        GNU C compiler
ii  gcc-13                          13.3.0-6ubuntu2~24.04                   amd64        GNU C compiler
ii  gcc-13-base:amd64               13.3.0-6ubuntu2~24.04                   amd64        GCC, the GNU Compiler Collection (base package)
ii  gcc-13-x86-64-linux-gnu         13.3.0-6ubuntu2~24.04                   amd64        GNU C compiler for the x86_64-linux-gnu architecture
ii  gcc-14-base:amd64               14.2.0-4ubuntu2~24.04                   amd64        GCC, the GNU Compiler Collection (base package)
ii  gcc-x86-64-linux-gnu            4:13.2.0-7ubuntu1                       amd64        GNU C compiler for the amd64 architecture
ii  gdisk                           1.0.10-1build1                          amd64        GPT fdisk text-mode partitioning tool
ii  gettext-base                    0.21-14ubuntu2                          amd64        GNU Internationalization utilities for the base system
ii  gir1.2-girepository-2.0:amd64   1.80.1-1                                amd64        Introspection data for GIRepository library
ii  gir1.2-glib-2.0:amd64           2.80.0-6ubuntu3.4                       amd64        Introspection data for GLib, GObject, Gio and GModule
ii  gir1.2-packagekitglib-1.0       1.2.8-2ubuntu1.2                        amd64        GObject introspection data for the PackageKit GLib library
ii  git                             1:2.43.0-1ubuntu7.3                     amd64        fast, scalable, distributed revision control system
ii  git-man                         1:2.43.0-1ubuntu7.3                     all          fast, scalable, distributed revision control system (manual pages)
ii  gnupg                           2.4.4-2ubuntu17.3                       all          GNU privacy guard - a free PGP replacement
ii  gnupg-l10n                      2.4.4-2ubuntu17.3                       all          GNU privacy guard - localization files
ii  gnupg-utils                     2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - utility programs
ii  gpg                             2.4.4-2ubuntu17.3                       amd64        GNU Privacy Guard -- minimalist public key operations
ii  gpg-agent                       2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - cryptographic agent
ii  gpg-wks-client                  2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - Web Key Service client
ii  gpgconf                         2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - core configuration utilities
ii  gpgsm                           2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - S/MIME version
ii  gpgv                            2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - signature verification tool
ii  grep                            3.11-4build1                            amd64        GNU grep, egrep and fgrep
ii  groff-base                      1.23.0-3build2                          amd64        GNU troff text-formatting system (base system components)
ii  gsettings-desktop-schemas       46.1-0ubuntu1                           all          GSettings desktop-wide schemas
ii  gtk-update-icon-cache           3.24.41-4ubuntu1.3                      amd64        icon theme caching utility
ii  gzip                            1.12-1ubuntu3.1                         amd64        GNU compression utilities
ii  hicolor-icon-theme              0.17-2                                  all          default fallback theme for FreeDesktop.org icon themes
ii  hostname                        3.23+nmu2ubuntu2                        amd64        utility to set/show the host name or domain name
ii  humanity-icon-theme             0.6.16                                  all          Humanity Icon theme
ii  info                            7.1-3build2                             amd64        Standalone GNU Info documentation browser
ii  init                            1.66ubuntu1                             amd64        metapackage ensuring an init system is installed
ii  init-system-helpers             1.66ubuntu1                             all          helper tools for all init systems
ii  install-info                    7.1-3build2                             amd64        Manage installed documentation in info format
ii  iproute2                        6.1.0-1ubuntu6                          amd64        networking and traffic control tools
ii  iputils-ping                    3:20240117-1ubuntu0.1                   amd64        Tools to test the reachability of network hosts
ii  iso-codes                       4.16.0-1                                all          ISO language, territory, currency, script codes and their translations
ii  javascript-common               11+nmu1                                 all          Base support for JavaScript library packages
ii  jp2a                            1.1.1-2ubuntu2                          amd64        converts jpg and png images to ascii
ii  kbd                             2.6.4-2ubuntu2                          amd64        Linux console font and keytable utilities
ii  keyboard-configuration          1.226ubuntu1                            all          system-wide keyboard preferences
ii  keyboxd                         2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - public key material service
ii  kmod                            31+20240202-2ubuntu7.1                  amd64        tools for managing Linux kernel modules
ii  krb5-locales                    1.20.1-6ubuntu2.6                       all          internationalization support for MIT Kerberos
ii  landscape-client                24.02-0ubuntu5.3                        amd64        Landscape administration system client
ii  landscape-common                24.02-0ubuntu5.3                        amd64        Landscape administration system client - Common files
ii  less                            590-2ubuntu2.1                          amd64        pager program similar to more
ii  libacl1:amd64                   2.3.2-1build1.1                         amd64        access control list - shared library
ii  libalgorithm-diff-perl          1.201-1                                 all          module to find differences between files
ii  libalgorithm-diff-xs-perl:amd64 0.04-8build3                            amd64        module to find differences between files (XS accelerated)
ii  libalgorithm-merge-perl         0.08-5                                  all          Perl module for three-way merge of textual data
ii  libaom3:amd64                   3.8.2-2ubuntu0.1                        amd64        AV1 Video Codec Library
ii  libapparmor1:amd64              4.0.1really4.0.1-0ubuntu0.24.04.4       amd64        changehat AppArmor library
ii  libappstream5:amd64             1.0.2-1build6                           amd64        Library to access AppStream services
ii  libapt-pkg6.0t64:amd64          2.8.3                                   amd64        package management runtime library
ii  libargon2-1:amd64               0~20190702+dfsg-4build1                 amd64        memory-hard hashing function - runtime library
ii  libasan8:amd64                  14.2.0-4ubuntu2~24.04                   amd64        AddressSanitizer -- a fast memory error detector
ii  libassuan0:amd64                2.5.6-1build1                           amd64        IPC library for the GnuPG components
ii  libatk-bridge2.0-0t64:amd64     2.52.0-1build1                          amd64        AT-SPI 2 toolkit bridge - shared library
ii  libatk1.0-0t64:amd64            2.52.0-1build1                          amd64        ATK accessibility toolkit
ii  libatm1t64:amd64                1:2.5.1-5.1build1                       amd64        shared library for ATM (Asynchronous Transfer Mode)
ii  libatomic1:amd64                14.2.0-4ubuntu2~24.04                   amd64        support library providing __atomic built-in functions
ii  libatspi2.0-0t64:amd64          2.52.0-1build1                          amd64        Assistive Technology Service Provider Interface - shared library
ii  libattr1:amd64                  1:2.5.2-1build1.1                       amd64        extended attribute handling - shared library
ii  libaudit-common                 1:3.1.2-2.1build1.1                     all          Dynamic library for security auditing - common files
ii  libaudit1:amd64                 1:3.1.2-2.1build1.1                     amd64        Dynamic library for security auditing
ii  libavahi-client3:amd64          0.8-13ubuntu6                           amd64        Avahi client library
ii  libavahi-common-data:amd64      0.8-13ubuntu6                           amd64        Avahi common data files
ii  libavahi-common3:amd64          0.8-13ubuntu6                           amd64        Avahi common library
ii  libbinutils:amd64               2.42-4ubuntu2.5                         amd64        GNU binary utilities (private shared library)
ii  libblkid1:amd64                 2.39.3-9ubuntu6.2                       amd64        block device ID library
ii  libbpf1:amd64                   1:1.3.0-2build2                         amd64        eBPF helper library (shared library)
ii  libbrotli1:amd64                1.1.0-2build2                           amd64        library implementing brotli encoder and decoder (shared libraries)
ii  libbsd0:amd64                   0.12.1-1build1.1                        amd64        utility functions from BSD systems - shared library
ii  libbz2-1.0:amd64                1.0.8-5.1build0.1                       amd64        high-quality block-sorting file compressor library - runtime
ii  libc-bin                        2.39-0ubuntu8.5                         amd64        GNU C Library: Binaries
ii  libc-dev-bin                    2.39-0ubuntu8.5                         amd64        GNU C Library: Development binaries
ii  libc-devtools                   2.39-0ubuntu8.5                         amd64        GNU C Library: Development tools
ii  libc6-dev:amd64                 2.39-0ubuntu8.5                         amd64        GNU C Library: Development Libraries and Header Files
ii  libc6:amd64                     2.39-0ubuntu8.5                         amd64        GNU C Library: Shared libraries
ii  libcairo-gobject2:amd64         1.18.0-3build1                          amd64        Cairo 2D vector graphics library (GObject library)
ii  libcairo2:amd64                 1.18.0-3build1                          amd64        Cairo 2D vector graphics library
ii  libcap-ng0:amd64                0.8.4-2build2                           amd64        alternate POSIX capabilities library
ii  libcap2-bin                     1:2.66-5ubuntu2.2                       amd64        POSIX 1003.1e capabilities (utilities)
ii  libcap2:amd64                   1:2.66-5ubuntu2.2                       amd64        POSIX 1003.1e capabilities (library)
ii  libcapstone-dev:amd64           4.0.2-5.1build1                         amd64        lightweight multi-architecture disassembly framework - devel files
ii  libcapstone4:amd64              4.0.2-5.1build1                         amd64        lightweight multi-architecture disassembly framework - library
ii  libcbor0.10:amd64               0.10.2-1.2ubuntu2                       amd64        library for parsing and generating CBOR (RFC 7049)
ii  libcc1-0:amd64                  14.2.0-4ubuntu2~24.04                   amd64        GCC cc1 plugin for GDB
ii  libcolord2:amd64                1.4.7-1build2                           amd64        system service to manage device colour profiles -- runtime
ii  libcom-err2:amd64               1.47.0-2.4~exp1ubuntu4.1                amd64        common error description library
ii  libcrypt-dev:amd64              1:4.4.36-4build1                        amd64        libcrypt development files
ii  libcrypt1:amd64                 1:4.4.36-4build1                        amd64        libcrypt shared library
ii  libcryptsetup12:amd64           2:2.7.0-1ubuntu4.2                      amd64        disk encryption support - shared library
ii  libctf-nobfd0:amd64             2.42-4ubuntu2.5                         amd64        Compact C Type Format library (runtime, no BFD dependency)
ii  libctf0:amd64                   2.42-4ubuntu2.5                         amd64        Compact C Type Format library (runtime, BFD dependency)
ii  libcups2t64:amd64               2.4.7-1.2ubuntu7.3                      amd64        Common UNIX Printing System(tm) - Core library
ii  libcurl3t64-gnutls:amd64        8.5.0-2ubuntu10.6                       amd64        easy-to-use client-side URL transfer library (GnuTLS flavour)
ii  libcurl4t64:amd64               8.5.0-2ubuntu10.6                       amd64        easy-to-use client-side URL transfer library (OpenSSL flavour)
ii  libdatrie1:amd64                0.2.13-3build1                          amd64        Double-array trie library
ii  libdb5.3t64:amd64               5.3.28+dfsg2-7                          amd64        Berkeley v5.3 Database Libraries [runtime]
ii  libdbus-1-3:amd64               1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (library)
ii  libdconf1:amd64                 0.40.0-4build2                          amd64        simple configuration storage system - runtime library
ii  libde265-0:amd64                1.0.15-1build3                          amd64        Open H.265 video codec implementation
ii  libdebconfclient0:amd64         0.271ubuntu3                            amd64        Debian Configuration Management System (C-implementation library)
ii  libdeflate0:amd64               1.19-1build1.1                          amd64        fast, whole-buffer DEFLATE-based compression and decompression
ii  libdevmapper1.02.1:amd64        2:1.02.185-3ubuntu3.2                   amd64        Linux Kernel Device Mapper userspace library
ii  libdpkg-perl                    1.22.6ubuntu6.1                         all          Dpkg perl modules
ii  libdrm-amdgpu1:amd64            2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to amdgpu-specific kernel DRM services -- runtime
ii  libdrm-common                   2.4.122-1~ubuntu0.24.04.1               all          Userspace interface to kernel DRM services -- common files
ii  libdrm-intel1:amd64             2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to intel-specific kernel DRM services -- runtime
ii  libdrm-nouveau2:amd64           2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to nouveau-specific kernel DRM services -- runtime
ii  libdrm-radeon1:amd64            2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to radeon-specific kernel DRM services -- runtime
ii  libdrm2:amd64                   2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to kernel DRM services -- runtime
ii  libduktape207:amd64             2.7.0+tests-0ubuntu3                    amd64        embeddable Javascript engine, library
ii  libdw1t64:amd64                 0.190-1.1ubuntu0.1                      amd64        library that provides access to the DWARF debug information
ii  libeatmydata1:amd64             131-1ubuntu1                            amd64        Library and utilities designed to disable fsync and friends - shared library     
ii  libedit2:amd64                  3.1-20230828-1build1                    amd64        BSD editline and history libraries
ii  libegl-mesa0:amd64              24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the EGL API -- Mesa vendor library
ii  libegl1:amd64                   1.7.0-1build1                           amd64        Vendor neutral GL dispatch library -- EGL support
ii  libelf1t64:amd64                0.190-1.1ubuntu0.1                      amd64        library to read and write ELF files
ii  libepoxy0:amd64                 1.5.10-1build1                          amd64        OpenGL function pointer management library
ii  liberror-perl                   0.17029-2                               all          Perl module for error/exception handling in an OO-ish way
ii  libestr0:amd64                  0.1.11-1build1                          amd64        Helper functions for handling strings (lib)
ii  libevent-core-2.1-7t64:amd64    2.1.12-stable-9ubuntu2                  amd64        Asynchronous event notification library (core)
ii  libexpat1:amd64                 2.6.1-2ubuntu0.3                        amd64        XML parsing C library - runtime library
ii  libext2fs2t64:amd64             1.47.0-2.4~exp1ubuntu4.1                amd64        ext2/ext3/ext4 file system libraries
ii  libfakeroot:amd64               1.33-1                                  amd64        tool for simulating superuser privileges - shared libraries
ii  libfastjson4:amd64              1.2304.0-1build1                        amd64        fast json library for C
ii  libfdisk1:amd64                 2.39.3-9ubuntu6.2                       amd64        fdisk partitioning library
ii  libffi8:amd64                   3.4.6-1build1                           amd64        Foreign Function Interface library runtime
ii  libfido2-1:amd64                1.14.0-1build3                          amd64        library for generating and verifying FIDO 2.0 objects
ii  libfile-fcntllock-perl          0.22-4ubuntu5                           amd64        Perl module for file locking with fcntl(2)
ii  libflac12t64:amd64              1.4.3+ds-2.1ubuntu2                     amd64        Free Lossless Audio Codec - runtime C library
ii  libfontconfig1:amd64            2.15.0-1.1ubuntu2                       amd64        generic font configuration library - runtime
ii  libfreetype6:amd64              2.13.2+dfsg-1build3                     amd64        FreeType 2 font engine, shared library files
ii  libfribidi0:amd64               1.0.13-3build1                          amd64        Free Implementation of the Unicode BiDi algorithm
ii  libfuse3-3:amd64                3.14.0-5build1                          amd64        Filesystem in Userspace (library) (3.x version)
ii  libgbm1:amd64                   24.2.8-1ubuntu1~24.04.1                 amd64        generic buffer management API -- runtime
ii  libgcc-13-dev:amd64             13.3.0-6ubuntu2~24.04                   amd64        GCC support library (development files)
ii  libgcc-s1:amd64                 14.2.0-4ubuntu2~24.04                   amd64        GCC support library
ii  libgcrypt20:amd64               1.10.3-2build1                          amd64        LGPL Crypto library - runtime library
ii  libgd3:amd64                    2.3.3-9ubuntu5                          amd64        GD Graphics Library
ii  libgdbm-compat4t64:amd64        1.23-5.1build1                          amd64        GNU dbm database routines (legacy support runtime version)
ii  libgdbm6t64:amd64               1.23-5.1build1                          amd64        GNU dbm database routines (runtime version)
ii  libgdk-pixbuf-2.0-0:amd64       2.42.10+dfsg-3ubuntu3.2                 amd64        GDK Pixbuf library
ii  libgdk-pixbuf2.0-bin            2.42.10+dfsg-3ubuntu3.2                 amd64        GDK Pixbuf library (thumbnailer)
ii  libgdk-pixbuf2.0-common         2.42.10+dfsg-3ubuntu3.2                 all          GDK Pixbuf library - data files
ii  libgirepository-1.0-1:amd64     1.80.1-1                                amd64        Library for handling GObject introspection data (runtime library)
ii  libgit2-1.7:amd64               1.7.2+ds-1ubuntu3                       amd64        low-level Git library
ii  libgl1-amber-dri:amd64          21.3.9-0ubuntu2                         amd64        free implementation of the OpenGL API -- Amber DRI modules
ii  libgl1-mesa-dri:amd64           24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the OpenGL API -- DRI modules
ii  libgl1:amd64                    1.7.0-1build1                           amd64        Vendor neutral GL dispatch library -- legacy GL support
ii  libglapi-mesa:amd64             24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the GL API -- shared library
ii  libglib2.0-0t64:amd64           2.80.0-6ubuntu3.4                       amd64        GLib library of C routines
ii  libglib2.0-bin                  2.80.0-6ubuntu3.4                       amd64        Programs for the GLib library
ii  libglib2.0-data                 2.80.0-6ubuntu3.4                       all          Common files for GLib library
ii  libglvnd0:amd64                 1.7.0-1build1                           amd64        Vendor neutral GL dispatch library
ii  libglx-mesa0:amd64              24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the OpenGL API -- GLX vendor library
ii  libglx0:amd64                   1.7.0-1build1                           amd64        Vendor neutral GL dispatch library -- GLX support
ii  libgmp10:amd64                  2:6.3.0+dfsg-2ubuntu6.1                 amd64        Multiprecision arithmetic library
ii  libgnutls30t64:amd64            3.8.3-1.1ubuntu3.4                      amd64        GNU TLS library - main runtime library
ii  libgomp1:amd64                  14.2.0-4ubuntu2~24.04                   amd64        GCC OpenMP (GOMP) support library
ii  libgpg-error-l10n               1.47-3build2.1                          all          library of error values and messages in GnuPG (localization files)
ii  libgpg-error0:amd64             1.47-3build2.1                          amd64        GnuPG development runtime library
ii  libgpm2:amd64                   1.20.7-11                               amd64        General Purpose Mouse - shared library
ii  libgprofng0:amd64               2.42-4ubuntu2.5                         amd64        GNU Next Generation profiler (runtime library)
ii  libgraphite2-3:amd64            1.3.14-2build1                          amd64        Font rendering engine for Complex Scripts -- library
ii  libgssapi-krb5-2:amd64          1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries - krb5 GSS-API Mechanism
ii  libgstreamer1.0-0:amd64         1.24.2-1ubuntu0.1                       amd64        Core GStreamer libraries and elements
ii  libgtk-3-0t64:amd64             3.24.41-4ubuntu1.3                      amd64        GTK graphical user interface library
ii  libgtk-3-bin                    3.24.41-4ubuntu1.3                      amd64        programs for the GTK graphical user interface library
ii  libgtk-3-common                 3.24.41-4ubuntu1.3                      all          common files for the GTK graphical user interface library
ii  libharfbuzz0b:amd64             8.3.0-2build2                           amd64        OpenType text shaping engine (shared library)
ii  libheif-plugin-aomdec:amd64     1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - aomdec plugin
ii  libheif-plugin-aomenc:amd64     1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - aomenc plugin
ii  libheif-plugin-libde265:amd64   1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - libde265 plugin
ii  libheif1:amd64                  1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - shared library
ii  libhogweed6t64:amd64            3.9.1-2.2build1.1                       amd64        low level cryptographic library (public-key cryptos)
ii  libhttp-parser2.9:amd64         2.9.4-6build1                           amd64        parser for HTTP messages written in C
ii  libhwasan0:amd64                14.2.0-4ubuntu2~24.04                   amd64        AddressSanitizer -- a fast memory error detector
ii  libicu74:amd64                  74.2-1ubuntu3.1                         amd64        International Components for Unicode
ii  libidn2-0:amd64                 2.3.7-2build1.1                         amd64        Internationalized domain names (IDNA2008/TR46) library
ii  libisl23:amd64                  0.26-3build1.1                          amd64        manipulating sets and relations of integer points bounded by linear constraints  
ii  libitm1:amd64                   14.2.0-4ubuntu2~24.04                   amd64        GNU Transactional Memory Library
ii  libjansson4:amd64               2.14-2build2                            amd64        C library for encoding, decoding and manipulating JSON data
ii  libjbig0:amd64                  2.1-6.1ubuntu2                          amd64        JBIGkit libraries
ii  libjpeg-turbo8:amd64            2.1.5-2ubuntu2                          amd64        libjpeg-turbo JPEG runtime library
ii  libjpeg8:amd64                  8c-2ubuntu11                            amd64        Independent JPEG Group's JPEG runtime library (dependency package)
ii  libjs-jquery                    3.6.1+dfsg+~3.5.14-1                    all          JavaScript library for dynamic web applications
ii  libjson-c5:amd64                0.17-1build1                            amd64        JSON manipulation library - shared library
ii  libk5crypto3:amd64              1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries - Crypto Library
ii  libkeyutils1:amd64              1.6.3-3build1                           amd64        Linux Key Management Utilities (library)
ii  libkmod2:amd64                  31+20240202-2ubuntu7.1                  amd64        libkmod shared library
ii  libkrb5-3:amd64                 1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries
ii  libkrb5support0:amd64           1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries - Support library
ii  libksba8:amd64                  1.6.6-1build1                           amd64        X.509 and CMS support library
ii  liblcms2-2:amd64                2.14-2build1                            amd64        Little CMS 2 color management library
ii  libldap-common                  2.6.7+dfsg-1~exp1ubuntu8.2              all          OpenLDAP common files for libraries
ii  libldap2:amd64                  2.6.7+dfsg-1~exp1ubuntu8.2              amd64        OpenLDAP libraries
ii  liblerc4:amd64                  4.0.0+ds-4ubuntu2                       amd64        Limited Error Raster Compression library
ii  libllvm19:amd64                 1:19.1.1-1ubuntu1~24.04.2               amd64        Modular compiler and toolchain technologies, runtime library
ii  liblocale-gettext-perl          1.07-6ubuntu5                           amd64        module using libc functions for internationalization in Perl
ii  liblsan0:amd64                  14.2.0-4ubuntu2~24.04                   amd64        LeakSanitizer -- a memory leak detector (runtime)
ii  liblz4-1:amd64                  1.9.4-1build1.1                         amd64        Fast LZ compression algorithm library - runtime
ii  liblz4-dev:amd64                1.9.4-1build1.1                         amd64        Fast LZ compression algorithm library - development files
ii  liblzma5:amd64                  5.6.1+really5.4.5-1ubuntu0.2            amd64        XZ-format compression library
ii  liblzo2-2:amd64                 2.10-2build4                            amd64        data compression library
ii  libmagic-dev:amd64              1:5.45-3build1                          amd64        Recognize the type of data in a file using "magic" numbers - development
ii  libmagic-mgc                    1:5.45-3build1                          amd64        File type determination library using "magic" numbers (compiled magic file)      
ii  libmagic1t64:amd64              1:5.45-3build1                          amd64        Recognize the type of data in a file using "magic" numbers - library
ii  libmd0:amd64                    1.1.0-2build1.1                         amd64        message digest functions from BSD systems - shared library
ii  libmnl0:amd64                   1.0.5-2build1                           amd64        minimalistic Netlink communication library
ii  libmount1:amd64                 2.39.3-9ubuntu6.2                       amd64        device mounting library
ii  libmp3lame0:amd64               3.100-6build1                           amd64        MP3 encoding library
ii  libmpc3:amd64                   1.3.1-1build1.1                         amd64        multiple precision complex floating-point library
ii  libmpfr6:amd64                  4.2.1-1build1.1                         amd64        multiple precision floating-point computation
ii  libmpg123-0t64:amd64            1.32.5-1ubuntu1.1                       amd64        MPEG layer 1/2/3 audio decoder (shared library)
ii  libncurses6:amd64               6.4+20240113-1ubuntu2                   amd64        shared libraries for terminal handling
ii  libncursesw6:amd64              6.4+20240113-1ubuntu2                   amd64        shared libraries for terminal handling (wide character support)
ii  libnetplan1:amd64               1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration runtime library
ii  libnettle8t64:amd64             3.9.1-2.2build1.1                       amd64        low level cryptographic library (symmetric and one-way cryptos)
ii  libnewt0.52:amd64               0.52.24-2ubuntu2                        amd64        Not Erik's Windowing Toolkit - text mode windowing with slang
ii  libnghttp2-14:amd64             1.59.0-1ubuntu0.2                       amd64        library implementing HTTP/2 protocol (shared library)
ii  libnpth0t64:amd64               1.6-3.1build1                           amd64        replacement for GNU Pth using system threads
ii  libnss-systemd:amd64            255.4-1ubuntu8.8                        amd64        nss module providing dynamic user and group name resolution
ii  libogg0:amd64                   1.3.5-3build1                           amd64        Ogg bitstream library
ii  libopus0:amd64                  1.4-1build1                             amd64        Opus codec runtime library
ii  libp11-kit0:amd64               0.25.3-4ubuntu2.1                       amd64        library for loading and coordinating access to PKCS#11 modules - runtime
ii  libpackagekit-glib2-18:amd64    1.2.8-2ubuntu1.2                        amd64        Library for accessing PackageKit using GLib
ii  libpam-cap:amd64                1:2.66-5ubuntu2.2                       amd64        POSIX 1003.1e capabilities (PAM module)
ii  libpam-modules-bin              1.5.3-5ubuntu5.4                        amd64        Pluggable Authentication Modules for PAM - helper binaries
ii  libpam-modules:amd64            1.5.3-5ubuntu5.4                        amd64        Pluggable Authentication Modules for PAM
ii  libpam-runtime                  1.5.3-5ubuntu5.4                        all          Runtime support for the PAM library
ii  libpam-systemd:amd64            255.4-1ubuntu8.8                        amd64        system and service manager - PAM module
ii  libpam0g:amd64                  1.5.3-5ubuntu5.4                        amd64        Pluggable Authentication Modules library
ii  libpango-1.0-0:amd64            1.52.1+ds-1build1                       amd64        Layout and rendering of internationalized text
ii  libpangocairo-1.0-0:amd64       1.52.1+ds-1build1                       amd64        Layout and rendering of internationalized text
ii  libpangoft2-1.0-0:amd64         1.52.1+ds-1build1                       amd64        Layout and rendering of internationalized text
ii  libpciaccess0:amd64             0.17-3ubuntu0.24.04.2                   amd64        Generic PCI access library for X
ii  libpcre2-8-0:amd64              10.42-4ubuntu2.1                        amd64        New Perl Compatible Regular Expression Library- 8 bit runtime files
ii  libperl5.38t64:amd64            5.38.2-3.2ubuntu0.2                     amd64        shared Perl library
ii  libpipeline1:amd64              1.5.7-2                                 amd64        Unix process pipeline manipulation library
ii  libpixman-1-0:amd64             0.42.2-1build1                          amd64        pixel-manipulation library for X and cairo
ii  libpng16-16t64:amd64            1.6.43-5build1                          amd64        PNG library - runtime (version 1.6)
ii  libpolkit-agent-1-0:amd64       124-2ubuntu1.24.04.2                    amd64        polkit Authentication Agent API
ii  libpolkit-gobject-1-0:amd64     124-2ubuntu1.24.04.2                    amd64        polkit Authorization API
ii  libpopt0:amd64                  1.19+dfsg-1build1                       amd64        lib for parsing cmdline parameters
ii  libproc2-0:amd64                2:4.0.4-4ubuntu3.2                      amd64        library for accessing process information from /proc
ii  libpsl5t64:amd64                0.21.2-1.1build1                        amd64        Library for Public Suffix List (shared libraries)
ii  libpython3-stdlib:amd64         3.12.3-0ubuntu2                         amd64        interactive high-level object-oriented language (default python3 version)        
ii  libpython3.12-minimal:amd64     3.12.3-1ubuntu0.7                       amd64        Minimal subset of the Python language (version 3.12)
ii  libpython3.12-stdlib:amd64      3.12.3-1ubuntu0.7                       amd64        Interactive high-level object-oriented language (standard library, version 3.12) 
ii  libpython3.12t64:amd64          3.12.3-1ubuntu0.7                       amd64        Shared Python runtime library (version 3.12)
ii  libquadmath0:amd64              14.2.0-4ubuntu2~24.04                   amd64        GCC Quad-Precision Math Library
ii  libradare2-5.0.0t64:amd64       5.5.0+dfsg-1.1ubuntu3                   amd64        libraries from the radare2 suite
ii  libradare2-common               5.5.0+dfsg-1.1ubuntu3                   all          arch independent files from the radare2 suite
ii  libradare2-dev                  5.5.0+dfsg-1.1ubuntu3                   amd64        devel files from the radare2 suite
ii  libreadline8t64:amd64           8.2-4build1                             amd64        GNU readline and history libraries, run-time libraries
ii  librsvg2-2:amd64                2.58.0+dfsg-1build1                     amd64        SAX-based renderer library for SVG files (runtime)
ii  librsvg2-common:amd64           2.58.0+dfsg-1build1                     amd64        SAX-based renderer library for SVG files (extra runtime)
ii  librtmp1:amd64                  2.4+20151223.gitfa8646d.1-2build7       amd64        toolkit for RTMP streams (shared library)
ii  libruby3.2:amd64                3.2.3-1ubuntu0.24.04.5                  amd64        Libraries necessary to run Ruby 3.2
ii  libruby:amd64                   1:3.2~ubuntu1                           amd64        Libraries necessary to run Ruby
ii  libsasl2-2:amd64                2.1.28+dfsg1-5ubuntu3.1                 amd64        Cyrus SASL - authentication abstraction library
ii  libsasl2-modules-db:amd64       2.1.28+dfsg1-5ubuntu3.1                 amd64        Cyrus SASL - pluggable authentication modules (DB)
ii  libsasl2-modules:amd64          2.1.28+dfsg1-5ubuntu3.1                 amd64        Cyrus SASL - pluggable authentication modules
ii  libseccomp2:amd64               2.5.5-1ubuntu3.1                        amd64        high level interface to Linux seccomp filter
ii  libselinux1:amd64               3.5-2ubuntu2.1                          amd64        SELinux runtime shared libraries
ii  libsemanage-common              3.5-1build5                             all          Common files for SELinux policy management libraries
ii  libsemanage2:amd64              3.5-1build5                             amd64        SELinux policy management library
ii  libsensors-config               1:3.6.0-9build1                         all          lm-sensors configuration files
ii  libsensors5:amd64               1:3.6.0-9build1                         amd64        library to read temperature/voltage/fan sensors
ii  libsepol2:amd64                 3.5-2build1                             amd64        SELinux library for manipulating binary security policies
ii  libsframe1:amd64                2.42-4ubuntu2.5                         amd64        Library to handle the SFrame format (runtime library)
ii  libsharpyuv0:amd64              1.3.2-0.4build3                         amd64        Library for sharp RGB to YUV conversion
ii  libsigsegv2:amd64               2.14-1ubuntu2                           amd64        Library for handling page faults in a portable way
ii  libslang2:amd64                 2.3.3-3build2                           amd64        S-Lang programming library - runtime version
ii  libsmartcols1:amd64             2.39.3-9ubuntu6.2                       amd64        smart column output alignment library
ii  libsndfile1:amd64               1.2.2-1ubuntu5.24.04.1                  amd64        Library for reading/writing audio files
ii  libsodium23:amd64               1.0.18-1build3                          amd64        Network communication, cryptography and signaturing library
ii  libsqlite3-0:amd64              3.45.1-1ubuntu2.4                       amd64        SQLite 3 shared library
ii  libss2:amd64                    1.47.0-2.4~exp1ubuntu4.1                amd64        command-line interface parsing library
ii  libssh-4:amd64                  0.10.6-2ubuntu0.1                       amd64        tiny C SSH library (OpenSSL flavor)
ii  libssh2-1t64:amd64              1.11.0-4.1build2                        amd64        SSH2 client-side library
ii  libssl3t64:amd64                3.0.13-0ubuntu3.5                       amd64        Secure Sockets Layer toolkit - shared libraries
ii  libstdc++-13-dev:amd64          13.3.0-6ubuntu2~24.04                   amd64        GNU Standard C++ Library v3 (development files)
ii  libstdc++6:amd64                14.2.0-4ubuntu2~24.04                   amd64        GNU Standard C++ Library v3
ii  libstemmer0d:amd64              2.2.0-4build1                           amd64        Snowball stemming algorithms for use in Information Retrieval
ii  libsystemd-shared:amd64         255.4-1ubuntu8.8                        amd64        systemd shared private library
ii  libsystemd0:amd64               255.4-1ubuntu8.8                        amd64        systemd utility library
ii  libtasn1-6:amd64                4.19.0-3ubuntu0.24.04.1                 amd64        Manage ASN.1 structures (runtime)
ii  libtext-charwidth-perl:amd64    0.04-11build3                           amd64        get display widths of characters on the terminal
ii  libtext-iconv-perl:amd64        1.7-8build3                             amd64        module to convert between character sets in Perl
ii  libtext-wrapi18n-perl           0.06-10                                 all          internationalized substitute of Text::Wrap
ii  libthai-data                    0.1.29-2build1                          all          Data files for Thai language support library
ii  libthai0:amd64                  0.1.29-2build1                          amd64        Thai language support library
ii  libtiff6:amd64                  4.5.1+git230720-4ubuntu2.2              amd64        Tag Image File Format (TIFF) library
ii  libtinfo6:amd64                 6.4+20240113-1ubuntu2                   amd64        shared low-level terminfo library for terminal handling
ii  libtirpc-common                 1.3.4+ds-1.1build1                      all          transport-independent RPC library - common files
ii  libtirpc3t64:amd64              1.3.4+ds-1.1build1                      amd64        transport-independent RPC library
ii  libtsan2:amd64                  14.2.0-4ubuntu2~24.04                   amd64        ThreadSanitizer -- a Valgrind-based detector of data races (runtime)
ii  libubsan1:amd64                 14.2.0-4ubuntu2~24.04                   amd64        UBSan -- undefined behaviour sanitizer (runtime)
ii  libuchardet0:amd64              0.0.8-1build1                           amd64        universal charset detection library - shared library
ii  libudev1:amd64                  255.4-1ubuntu8.8                        amd64        libudev shared library
ii  libunistring5:amd64             1.1-2build1.1                           amd64        Unicode string library for C
ii  libunwind8:amd64                1.6.2-3build1.1                         amd64        library to determine the call-chain of a program - runtime
ii  libutempter0:amd64              1.2.1-3build1                           amd64        privileged helper for utmp/wtmp updates (runtime)
ii  libuuid1:amd64                  2.39.3-9ubuntu6.2                       amd64        Universally Unique ID library
ii  libuv1-dev:amd64                1.48.0-1.1build1                        amd64        asynchronous event notification library - development files
ii  libuv1t64:amd64                 1.48.0-1.1build1                        amd64        asynchronous event notification library - runtime library
ii  libvorbis0a:amd64               1.3.7-1build3                           amd64        decoder library for Vorbis General Audio Compression Codec
ii  libvorbisenc2:amd64             1.3.7-1build3                           amd64        encoder library for Vorbis General Audio Compression Codec
ii  libvulkan1:amd64                1.3.275.0-1build1                       amd64        Vulkan loader library
ii  libwayland-client0:amd64        1.22.0-2.1build1                        amd64        wayland compositor infrastructure - client library
ii  libwayland-cursor0:amd64        1.22.0-2.1build1                        amd64        wayland compositor infrastructure - cursor library
ii  libwayland-egl1:amd64           1.22.0-2.1build1                        amd64        wayland compositor infrastructure - EGL library
ii  libwayland-server0:amd64        1.22.0-2.1build1                        amd64        wayland compositor infrastructure - server library
ii  libwebp7:amd64                  1.3.2-0.4build3                         amd64        Lossy compression of digital photographic images
ii  libx11-6:amd64                  2:1.8.7-1build1                         amd64        X11 client-side library
ii  libx11-data                     2:1.8.7-1build1                         all          X11 client-side library
ii  libx11-xcb1:amd64               2:1.8.7-1build1                         amd64        Xlib/XCB interface library
ii  libxau6:amd64                   1:1.0.9-1build6                         amd64        X11 authorisation library
ii  libxcb-dri2-0:amd64             1.15-1ubuntu2                           amd64        X C Binding, dri2 extension
ii  libxcb-dri3-0:amd64             1.15-1ubuntu2                           amd64        X C Binding, dri3 extension
ii  libxcb-glx0:amd64               1.15-1ubuntu2                           amd64        X C Binding, glx extension
ii  libxcb-present0:amd64           1.15-1ubuntu2                           amd64        X C Binding, present extension
ii  libxcb-randr0:amd64             1.15-1ubuntu2                           amd64        X C Binding, randr extension
ii  libxcb-render0:amd64            1.15-1ubuntu2                           amd64        X C Binding, render extension
ii  libxcb-shm0:amd64               1.15-1ubuntu2                           amd64        X C Binding, shm extension
ii  libxcb-sync1:amd64              1.15-1ubuntu2                           amd64        X C Binding, sync extension
ii  libxcb-xfixes0:amd64            1.15-1ubuntu2                           amd64        X C Binding, xfixes extension
ii  libxcb1:amd64                   1.15-1ubuntu2                           amd64        X C Binding
ii  libxcomposite1:amd64            1:0.4.5-1build3                         amd64        X11 Composite extension library
ii  libxcursor1:amd64               1:1.2.1-1build1                         amd64        X cursor management library
ii  libxdamage1:amd64               1:1.1.6-1build1                         amd64        X11 damaged region extension library
ii  libxdmcp6:amd64                 1:1.1.3-0ubuntu6                        amd64        X11 Display Manager Control Protocol library
ii  libxext6:amd64                  2:1.3.4-1build2                         amd64        X11 miscellaneous extension library
ii  libxfixes3:amd64                1:6.0.0-2build1                         amd64        X11 miscellaneous 'fixes' extension library
ii  libxi6:amd64                    2:1.8.1-1build1                         amd64        X11 Input extension library
ii  libxinerama1:amd64              2:1.1.4-3build1                         amd64        X11 Xinerama extension library
ii  libxkbcommon0:amd64             1.6.0-1build1                           amd64        library interface to the XKB compiler - shared library
ii  libxml2:amd64                   2.9.14+dfsg-1.3ubuntu3.3                amd64        GNOME XML library
ii  libxmlb2:amd64                  0.3.18-1                                amd64        Binary XML library
ii  libxmuu1:amd64                  2:1.1.3-3build2                         amd64        X11 miscellaneous micro-utility library
ii  libxpm4:amd64                   1:3.5.17-1build2                        amd64        X11 pixmap library
ii  libxrandr2:amd64                2:1.5.2-2build1                         amd64        X11 RandR extension library
ii  libxrender1:amd64               1:0.9.10-1.1build1                      amd64        X Rendering Extension client library
ii  libxshmfence1:amd64             1.3-1build5                             amd64        X shared memory fences - shared library
ii  libxtables12:amd64              1.8.10-3ubuntu2                         amd64        netfilter xtables library
ii  libxtst6:amd64                  2:1.2.3-1.1build1                       amd64        X11 Testing -- Record extension library
ii  libxxf86vm1:amd64               1:1.1.4-1build4                         amd64        X11 XFree86 video mode extension library
ii  libxxhash0:amd64                0.8.2-2build1                           amd64        shared library for xxhash
ii  libyaml-0-2:amd64               0.2.5-1build1                           amd64        Fast YAML 1.1 parser and emitter library
ii  libzip-dev:amd64                1.7.3-1.1ubuntu2                        amd64        library for reading, creating, and modifying zip archives (development)
ii  libzip4t64:amd64                1.7.3-1.1ubuntu2                        amd64        library for reading, creating, and modifying zip archives (runtime)
ii  libzstd1:amd64                  1.5.5+dfsg2-2build1.1                   amd64        fast lossless compression algorithm
ii  linux-libc-dev:amd64            6.8.0-71.71                             amd64        Linux Kernel Headers for development
ii  locales                         2.39-0ubuntu8.5                         all          GNU C Library: National Language (locale) data [support]
ii  login                           1:4.13+dfsg1-4ubuntu3.2                 amd64        system login tools
ii  logrotate                       3.21.0-2build1                          amd64        Log rotation utility
ii  logsave                         1.47.0-2.4~exp1ubuntu4.1                amd64        save the output of a command in a log file
ii  lolcat                          100.0.1-3                               all          colorful `cat`
ii  lsb-release                     12.0-2                                  all          Linux Standard Base version reporting utility (minimal implementation)
ii  lshw                            02.19.git.2021.06.19.996aaad9c7-2build3 amd64        information about hardware configuration
ii  lsof                            4.95.0-1build3                          amd64        utility to list open files
ii  lto-disabled-list               47                                      all          list of packages not to build with LTO
ii  make                            4.3-4.1build2                           amd64        utility for directing compilation
ii  man-db                          2.12.0-4build2                          amd64        tools for reading manual pages
ii  manpages                        6.7-2                                   all          Manual pages about using a GNU/Linux system
ii  manpages-dev                    6.7-2                                   all          Manual pages about using GNU/Linux for development
ii  mawk                            1.3.4.20240123-1build1                  amd64        Pattern scanning and text processing language
ii  media-types                     10.1.0                                  all          List of standard media types and their usual file extension
ii  mesa-libgallium:amd64           24.2.8-1ubuntu1~24.04.1                 amd64        shared infrastructure for Mesa drivers
ii  mesa-vulkan-drivers:amd64       24.2.8-1ubuntu1~24.04.1                 amd64        Mesa Vulkan graphics drivers
ii  motd-news-config                13ubuntu10.2                            all          Configuration for motd-news shipped in base-files
ii  mount                           2.39.3-9ubuntu6.2                       amd64        tools for mounting and manipulating filesystems
ii  nano                            7.2-2ubuntu0.1                          amd64        small, friendly text editor inspired by Pico
ii  ncurses-base                    6.4+20240113-1ubuntu2                   all          basic terminal type definitions
ii  ncurses-bin                     6.4+20240113-1ubuntu2                   amd64        terminal-related programs and man pages
ii  netbase                         6.4                                     all          Basic TCP/IP networking system
ii  netcat-openbsd                  1.226-1ubuntu2                          amd64        TCP/IP swiss army knife
ii  netplan-generator               1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration for various backends at boot
ii  netplan.io                      1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration for various backends at runtime
ii  networkd-dispatcher             2.2.4-1                                 all          Dispatcher service for systemd-networkd connection status changes
ii  openssh-client                  1:9.6p1-3ubuntu13.12                    amd64        secure shell (SSH) client, for secure access to remote machines
ii  openssl                         3.0.13-0ubuntu3.5                       amd64        Secure Sockets Layer toolkit - cryptographic utility
ii  packagekit                      1.2.8-2ubuntu1.2                        amd64        Provides a package management service
ii  packagekit-tools                1.2.8-2ubuntu1.2                        amd64        Provides PackageKit command-line tools
ii  passwd                          1:4.13+dfsg1-4ubuntu3.2                 amd64        change and administer password and group data
ii  pastebinit                      1.6.2-1                                 all          command-line pastebin client
ii  patch                           2.7.6-7build3                           amd64        Apply a diff file to an original
ii  pci.ids                         0.0~2024.03.31-1ubuntu0.1               all          PCI ID Repository
ii  perl                            5.38.2-3.2ubuntu0.2                     amd64        Larry Wall's Practical Extraction and Report Language
ii  perl-base                       5.38.2-3.2ubuntu0.2                     amd64        minimal Perl system
ii  perl-modules-5.38               5.38.2-3.2ubuntu0.2                     all          Core Perl modules
ii  pinentry-curses                 1.2.1-3ubuntu5                          amd64        curses-based PIN or pass-phrase entry dialog for GnuPG
ii  polkitd                         124-2ubuntu1.24.04.2                    amd64        framework for managing administrative policies and privileges
ii  procps                          2:4.0.4-4ubuntu3.2                      amd64        /proc file system utilities
ii  psmisc                          23.7-1build1                            amd64        utilities that use the proc file system
ii  publicsuffix                    20231001.0357-0.1                       all          accurate, machine-readable list of domain name suffixes
ii  python-apt-common               2.7.7ubuntu4                            all          Python interface to libapt-pkg (locales)
ii  python-babel-localedata         2.10.3-3build1                          all          tools for internationalizing Python applications - locale data files
ii  python3                         3.12.3-0ubuntu2                         amd64        interactive high-level object-oriented language (default python3 version)        
ii  python3-apport                  2.28.1-0ubuntu3.8                       all          Python 3 library for Apport crash report handling
ii  python3-apt                     2.7.7ubuntu4                            amd64        Python 3 interface to libapt-pkg
ii  python3-attr                    23.2.0-2                                all          Attributes without boilerplate (Python 3)
ii  python3-automat                 22.10.0-2                               all          Self-service finite-state machines for the programmer on the go
ii  python3-babel                   2.10.3-3build1                          all          tools for internationalizing Python applications - Python 3.x
ii  python3-bcrypt                  3.2.2-1build1                           amd64        password hashing library for Python 3
ii  python3-blinker                 1.7.0-1                                 all          Fast, simple object-to-object and broadcast signaling (Python3)
ii  python3-certifi                 2023.11.17-1                            all          root certificates for validating SSL certs and verifying TLS hosts (python3)     
ii  python3-cffi-backend:amd64      1.16.0-2build1                          amd64        Foreign Function Interface for Python 3 calling C code - runtime
ii  python3-chardet                 5.2.0+dfsg-1                            all          Universal Character Encoding Detector (Python3)
ii  python3-click                   8.1.6-2                                 all          Wrapper around optparse for command line utilities - Python 3.x
ii  python3-colorama                0.4.6-4                                 all          Cross-platform colored terminal text in Python - Python 3.x
ii  python3-commandnotfound         23.04.0                                 all          Python 3 bindings for command-not-found.
ii  python3-configobj               5.0.8-3                                 all          simple but powerful config file reader and writer for Python 3
ii  python3-constantly              23.10.4-1                               all          Symbolic constants in Python
ii  python3-cryptography            41.0.7-4ubuntu0.1                       amd64        Python library exposing cryptographic recipes and primitives (Python 3)
ii  python3-dbus                    1.3.2-5build3                           amd64        simple interprocess messaging system (Python 3 interface)
ii  python3-debconf                 1.5.86ubuntu1                           all          interact with debconf from Python 3
ii  python3-distro                  1.9.0-1                                 all          Linux OS platform information API
ii  python3-distro-info             1.7build1                               all          information about distributions' releases (Python 3 module)
ii  python3-distupgrade             1:24.04.26                              all          manage release upgrades
ii  python3-gdbm:amd64              3.12.3-0ubuntu1                         amd64        GNU dbm database support for Python 3.x
ii  python3-gi                      3.48.2-1                                amd64        Python 3 bindings for gobject-introspection libraries
ii  python3-hamcrest                2.1.0-1                                 all          Hamcrest framework for matcher objects (Python 3)
ii  python3-httplib2                0.20.4-3                                all          comprehensive HTTP client library written for Python3
ii  python3-hyperlink               21.0.0-5                                all          Immutable, Pythonic, correct URLs.
ii  python3-idna                    3.6-2ubuntu0.1                          all          Python IDNA2008 (RFC 5891) handling (Python 3)
ii  python3-incremental             22.10.0-1                               all          Library for versioning Python projects
ii  python3-jinja2                  3.1.2-1ubuntu1.3                        all          small but fast and easy to use stand-alone template engine
ii  python3-json-pointer            2.0-0ubuntu1                            all          resolve JSON pointers - Python 3.x
ii  python3-jsonpatch               1.32-3                                  all          library to apply JSON patches - Python 3.x
ii  python3-jsonschema              4.10.3-2ubuntu1                         all          An(other) implementation of JSON Schema (Draft 3, 4, 6, 7)
ii  python3-jwt                     2.7.0-1                                 all          Python 3 implementation of JSON Web Token
ii  python3-launchpadlib            1.11.0-6                                all          Launchpad web services client library (Python 3)
ii  python3-lazr.restfulclient      0.14.6-1                                all          client for lazr.restful-based web services (Python 3)
ii  python3-lazr.uri                1.0.6-3                                 all          library for parsing, manipulating, and generating URIs
ii  python3-markdown-it             3.0.0-2                                 all          Python port of markdown-it and some its associated plugins
ii  python3-markupsafe              2.1.5-1build2                           amd64        HTML/XHTML/XML string library
ii  python3-mdurl                   0.1.2-1                                 all          Python port of the JavaScript mdurl package
ii  python3-minimal                 3.12.3-0ubuntu2                         amd64        minimal subset of the Python language (default python3 version)
ii  python3-netifaces:amd64         0.11.0-2build3                          amd64        portable network interface information - Python 3.x
ii  python3-netplan                 1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration Python bindings
ii  python3-newt:amd64              0.52.24-2ubuntu2                        amd64        NEWT module for Python3
ii  python3-oauthlib                3.2.2-1                                 all          generic, spec-compliant implementation of OAuth for Python3
ii  python3-openssl                 23.2.0-1                                all          Python 3 wrapper around the OpenSSL library
ii  python3-pkg-resources           68.1.2-2ubuntu1.2                       all          Package Discovery and Resource Access using pkg_resources
ii  python3-problem-report          2.28.1-0ubuntu3.8                       all          Python 3 library to handle problem reports
ii  python3-pyasn1                  0.4.8-4                                 all          ASN.1 library for Python (Python 3 module)
ii  python3-pyasn1-modules          0.2.8-1                                 all          Collection of protocols modules written in ASN.1 language (Python 3)
ii  python3-pycurl                  7.45.3-1build2                          amd64        Python bindings to libcurl (Python 3)
ii  python3-pygments                2.17.2+dfsg-1                           all          syntax highlighting package written in Python 3
ii  python3-pyparsing               3.1.1-1                                 all          alternative to creating and executing simple grammars - Python 3.x
ii  python3-pyrsistent:amd64        0.20.0-1build2                          amd64        persistent/functional/immutable data structures for Python
ii  python3-requests                2.31.0+dfsg-1ubuntu1.1                  all          elegant and simple HTTP library for Python3, built for human beings
ii  python3-rich                    13.7.1-1                                all          render rich text, tables, progress bars, syntax highlighting, markdown and more  
ii  python3-serial                  3.5-2                                   all          pyserial - module encapsulating access for the serial port
ii  python3-service-identity        24.1.0-1                                all          Service identity verification for pyOpenSSL (Python 3 module)
ii  python3-setuptools              68.1.2-2ubuntu1.2                       all          Python3 Distutils Enhancements
ii  python3-six                     1.16.0-4                                all          Python 2 and 3 compatibility library
ii  python3-software-properties     0.99.49.2                               all          manage the repositories that you install software from
ii  python3-systemd                 235-1build4                             amd64        Python 3 bindings for systemd
ii  python3-twisted                 24.3.0-1ubuntu0.1                       all          Event-based framework for internet applications
ii  python3-typing-extensions       4.10.0-1                                all          Backported and Experimental Type Hints for Python
ii  python3-tz                      2024.1-2                                all          Python3 version of the Olson timezone database
ii  python3-update-manager          1:24.04.12                              all          Python 3.x module for update-manager
ii  python3-urllib3                 2.0.7-1ubuntu0.2                        all          HTTP library with thread-safe connection pooling for Python3
ii  python3-wadllib                 1.3.6-5                                 all          Python 3 library for navigating WADL files
ii  python3-yaml                    6.0.1-2build2                           amd64        YAML parser and emitter for Python3
ii  python3-zope.interface          6.1-1build1                             amd64        Interfaces for Python3
ii  python3.12                      3.12.3-1ubuntu0.7                       amd64        Interactive high-level object-oriented language (version 3.12)
ii  python3.12-minimal              3.12.3-1ubuntu0.7                       amd64        Minimal subset of the Python language (version 3.12)
ii  radare2                         5.5.0+dfsg-1.1ubuntu3                   amd64        free and advanced command line hexadecimal editor
ii  rake                            13.0.6-3                                all          ruby make-like utility
ii  readline-common                 8.2-4build1                             all          GNU readline and history libraries, common files
ii  rpcsvc-proto                    1.4.2-0ubuntu7                          amd64        RPC protocol compiler and definitions
ii  rsync                           3.2.7-1ubuntu1.2                        amd64        fast, versatile, remote (and local) file-copying tool
ii  rsyslog                         8.2312.0-3ubuntu9.1                     amd64        reliable system and kernel logging daemon
ii  ruby                            1:3.2~ubuntu1                           amd64        Interpreter of object-oriented scripting language Ruby (default version)
ii  ruby-net-telnet                 0.2.0-1                                 all          telnet client library
ii  ruby-optimist                   3.0.0-2                                 all          Commandline parser for Ruby that just gets out of your way
ii  ruby-paint                      2.2.0-1                                 all          terminal paint library with 256 color and effect support
ii  ruby-rubygems                   3.4.20-1                                all          Package management framework for Ruby
ii  ruby-sdbm:amd64                 1.0.0-5build4                           amd64        simple file-based key-value store with String keys and values
ii  ruby-webrick                    1.8.1-1ubuntu0.1                        all          HTTP server toolkit in Ruby
ii  ruby-xmlrpc                     0.3.2-2                                 all          XMLRPC library for Ruby
ii  ruby3.2                         3.2.3-1ubuntu0.24.04.5                  amd64        Interpreter of object-oriented scripting language Ruby
ii  rubygems-integration            1.18                                    all          integration of Debian Ruby packages with Rubygems
ii  run-one                         1.17-0ubuntu2                           all          run just one instance of a command and its args at a time
ii  sed                             4.9-2build1                             amd64        GNU stream editor for filtering/transforming text
ii  sensible-utils                  0.0.22                                  all          Utilities for sensible alternative selection
ii  session-migration               0.3.9build1                             amd64        Tool to migrate in user session settings
ii  sgml-base                       1.31                                    all          SGML infrastructure and SGML catalog file support
ii  shared-mime-info                2.4-4                                   amd64        FreeDesktop.org shared MIME database and spec
ii  show-motd                       3.12                                    all          show message of the day in interactive shells
ii  snapd                           2.67.1+24.04                            amd64        Daemon and tooling that enable snap packages
ii  software-properties-common      0.99.49.2                               all          manage the repositories that you install software from (common)
ii  squashfs-tools                  1:4.6.1-1build1                         amd64        Tool to create and append to squashfs filesystems
ii  sudo                            1.9.15p5-3ubuntu5.24.04.1               amd64        Provide limited super user privileges to specific users
ii  systemd                         255.4-1ubuntu8.8                        amd64        system and service manager
ii  systemd-dev                     255.4-1ubuntu8.8                        all          systemd development files
ii  systemd-hwe-hwdb                255.1.4                                 all          udev rules for hardware enablement (HWE)
ii  systemd-resolved                255.4-1ubuntu8.8                        amd64        systemd DNS resolver
ii  systemd-sysv                    255.4-1ubuntu8.8                        amd64        system and service manager - SysV compatibility symlinks
ii  systemd-timesyncd               255.4-1ubuntu8.8                        amd64        minimalistic service to synchronize local time with NTP servers
ii  sysvinit-utils                  3.08-6ubuntu3                           amd64        System-V-like utilities
ii  tar                             1.35+dfsg-3build1                       amd64        GNU version of the tar archiving utility
ii  time                            1.9-0.2build1                           amd64        GNU time program for measuring CPU resource usage
ii  tmux                            3.4-1ubuntu0.1                          amd64        terminal multiplexer
ii  tzdata                          2025b-0ubuntu0.24.04.1                  all          time zone and daylight-saving time data
ii  ubuntu-keyring                  2023.11.28.1                            all          GnuPG keys of the Ubuntu archive
ii  ubuntu-minimal                  1.539.2                                 amd64        Minimal core of Ubuntu
ii  ubuntu-mono                     24.04-0ubuntu1                          all          Ubuntu Mono Icon theme
ii  ubuntu-pro-client               35.1ubuntu0~24.04                       amd64        Management tools for Ubuntu Pro
ii  ubuntu-pro-client-l10n          35.1ubuntu0~24.04                       amd64        Translations for Ubuntu Pro Client
ii  ubuntu-release-upgrader-core    1:24.04.26                              all          manage release upgrades
ii  ubuntu-wsl                      1.539.2                                 amd64        Ubuntu on Windows tools - Windows Subsystem for Linux integration
ii  ucf                             3.0043+nmu1                             all          Update Configuration File(s): preserve user changes to config files
ii  udev                            255.4-1ubuntu8.8                        amd64        /dev/ and hotplug management daemon
ii  unattended-upgrades             2.9.1+nmu4ubuntu1                       all          automatic installation of security upgrades
ii  unzip                           6.0-28ubuntu4.1                         amd64        De-archiver for .zip files
ii  update-manager-core             1:24.04.12                              all          manage release upgrades
ii  update-motd                     3.12                                    all          complements pam_motd in libpam-modules
ii  usb.ids                         2024.03.18-1                            all          USB ID Repository
ii  util-linux                      2.39.3-9ubuntu6.2                       amd64        miscellaneous system utilities
ii  uuid-runtime                    2.39.3-9ubuntu6.2                       amd64        runtime components for the Universally Unique ID library
ii  vim                             2:9.1.0016-1ubuntu7.8                   amd64        Vi IMproved - enhanced vi editor
ii  vim-common                      2:9.1.0016-1ubuntu7.8                   all          Vi IMproved - Common files
ii  vim-runtime                     2:9.1.0016-1ubuntu7.8                   all          Vi IMproved - Runtime files
ii  vim-tiny                        2:9.1.0016-1ubuntu7.8                   amd64        Vi IMproved - enhanced vi editor - compact version
ii  wget                            1.21.4-1ubuntu4.1                       amd64        retrieves files from the web
ii  whiptail                        0.52.24-2ubuntu2                        amd64        Displays user-friendly dialog boxes from shell scripts
ii  wsl-pro-service                 0.1.4                                   amd64        Ubuntu Pro for WSL - WSL service
ii  wsl-setup                       0.5.8~24.04                             amd64        WSL integration setup
ii  x11-common                      1:7.7+23ubuntu3                         all          X Window System (X.Org) infrastructure
ii  xauth                           1:1.1.2-1build1                         amd64        X authentication utility
ii  xdg-user-dirs                   0.18-1build1                            amd64        tool to manage well known user directories
ii  xkb-data                        2.41-2ubuntu1.1                         all          X Keyboard Extension (XKB) configuration data
ii  xml-core                        0.19                                    all          XML infrastructure and XML catalog file support
ii  xxd                             2:9.1.0016-1ubuntu7.8                   amd64        tool to make (or reverse) a hex dump
ii  xz-utils                        5.6.1+really5.4.5-1ubuntu0.2            amd64        XZ-format compression utilities
ii  zip                             3.0-13ubuntu0.2                         amd64        Archiver for .zip files
ii  zlib1g-dev:amd64                1:1.3.dfsg-3.1ubuntu2.1                 amd64        compression library - development
ii  zlib1g:amd64                    1:1.3.dfsg-3.1ubuntu2.1                 amd64        compression library - runtime
ii  zsh                             5.9-6ubuntu2                            amd64        shell with lots of features
ii  zsh-common                      5.9-6ubuntu2                            all          architecture independent files for Zsh





PS C:\AI\api> wsl -d Ubuntu-24.04 -- bash -c "echo '=== 所有已安装的apt包 ===' && dpkg -l | grep '^ii' | awk '{print $2}' | sort"
wsl: 检测到 localhost 代理配置，但未镜像到 WSL。NAT 模式下的 WSL 不支持 localhost 代理。
=== 所有已安装的apt包 ===
ii  adduser                         3.137ubuntu1                            all          add and remove users and groups
ii  adwaita-icon-theme              46.0-1                                  all          default icon theme of GNOME
ii  apparmor                        4.0.1really4.0.1-0ubuntu0.24.04.4       amd64        user-space parser utility for AppArmor
ii  apport                          2.28.1-0ubuntu3.8                       all          automatically generate crash reports for debugging
ii  apport-core-dump-handler        2.28.1-0ubuntu3.8                       all          Kernel core dump handler for Apport
ii  apport-symptoms                 0.25                                    all          symptom scripts for apport
ii  appstream                       1.0.2-1build6                           amd64        Software component metadata management
ii  apt                             2.8.3                                   amd64        commandline package manager
ii  apt-utils                       2.8.3                                   amd64        package management related utility programs
ii  at-spi2-common                  2.52.0-1build1                          all          Assistive Technology Service Provider Interface (common files)
ii  at-spi2-core                    2.52.0-1build1                          amd64        Assistive Technology Service Provider Interface (D-Bus core)
ii  base-files                      13ubuntu10.2                            amd64        Debian base system miscellaneous files
ii  base-passwd                     3.6.3build1                             amd64        Debian base system master password and group files
ii  bash                            5.2.21-2ubuntu4                         amd64        GNU Bourne Again SHell
ii  bash-completion                 1:2.11-8                                all          programmable completion for the bash shell
ii  bc                              1.07.1-3ubuntu4                         amd64        GNU bc arbitrary precision calculator language
ii  binutils                        2.42-4ubuntu2.5                         amd64        GNU assembler, linker and binary utilities
ii  binutils-common:amd64           2.42-4ubuntu2.5                         amd64        Common files for the GNU assembler, linker and binary utilities
ii  binutils-x86-64-linux-gnu       2.42-4ubuntu2.5                         amd64        GNU binary utilities, for x86-64-linux-gnu target
ii  bsdextrautils                   2.39.3-9ubuntu6.2                       amd64        extra utilities from 4.4BSD-Lite
ii  bsdutils                        1:2.39.3-9ubuntu6.2                     amd64        basic utilities from 4.4BSD-Lite
ii  build-essential                 12.10ubuntu1                            amd64        Informational list of build-essential packages
ii  byobu                           6.11-0ubuntu1                           all          text window manager, shell multiplexer, integrated DevOps environment
ii  bzip2                           1.0.8-5.1build0.1                       amd64        high-quality block-sorting file compressor - utilities
ii  ca-certificates                 20240203                                all          Common CA certificates
ii  catimg                          2.7.0-2                                 amd64        fast image printing in to your terminal
ii  cloud-guest-utils               0.33-1                                  all          cloud guest utilities
ii  cloud-init                      25.1.4-0ubuntu0~24.04.1                 all          initialization and customization tool for cloud instances
ii  command-not-found               23.04.0                                 all          Suggest installation of packages in interactive bash sessions
ii  console-setup                   1.226ubuntu1                            all          console font and keymap setup program
ii  console-setup-linux             1.226ubuntu1                            all          Linux specific part of console-setup
ii  coreutils                       9.4-3ubuntu6                            amd64        GNU core utilities
ii  cpp                             4:13.2.0-7ubuntu1                       amd64        GNU C preprocessor (cpp)
ii  cpp-13                          13.3.0-6ubuntu2~24.04                   amd64        GNU C preprocessor
ii  cpp-13-x86-64-linux-gnu         13.3.0-6ubuntu2~24.04                   amd64        GNU C preprocessor for x86_64-linux-gnu
ii  cpp-x86-64-linux-gnu            4:13.2.0-7ubuntu1                       amd64        GNU C preprocessor (cpp) for the amd64 architecture
ii  cron                            3.0pl1-184ubuntu2                       amd64        process scheduling daemon
ii  cron-daemon-common              3.0pl1-184ubuntu2                       all          process scheduling daemon's configuration files
ii  curl                            8.5.0-2ubuntu10.6                       amd64        command line tool for transferring data with URL syntax
ii  dash                            0.5.12-6ubuntu5                         amd64        POSIX-compliant shell
ii  dbus                            1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (system message bus)
ii  dbus-bin                        1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (command line utilities)
ii  dbus-daemon                     1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (reference message bus)
ii  dbus-session-bus-common         1.14.10-4ubuntu4.1                      all          simple interprocess messaging system (session bus configuration)
ii  dbus-system-bus-common          1.14.10-4ubuntu4.1                      all          simple interprocess messaging system (system bus configuration)
ii  dbus-user-session               1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (systemd --user integration)
ii  dbus-x11                        1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (X11 deps)
ii  dconf-gsettings-backend:amd64   0.40.0-4build2                          amd64        simple configuration storage system - GSettings back-end
ii  dconf-service                   0.40.0-4build2                          amd64        simple configuration storage system - D-Bus service
ii  debconf                         1.5.86ubuntu1                           all          Debian configuration management system
ii  debconf-i18n                    1.5.86ubuntu1                           all          full internationalization support for debconf
ii  debianutils                     5.17build1                              amd64        Miscellaneous utilities specific to Debian
ii  dhcpcd-base                     1:10.0.6-1ubuntu3.1                     amd64        DHCPv4 and DHCPv6 dual-stack client (binaries and exit hooks)
ii  diffutils                       1:3.10-1build1                          amd64        File comparison utilities
ii  dirmngr                         2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - network certificate management service
ii  distro-info                     1.7build1                               amd64        provides information about the distributions' releases
ii  distro-info-data                0.60ubuntu0.3                           all          information about the distributions' releases (data files)
ii  dmsetup                         2:1.02.185-3ubuntu3.2                   amd64        Linux Kernel Device Mapper userspace library
ii  dpkg                            1.22.6ubuntu6.1                         amd64        Debian package management system
ii  dpkg-dev                        1.22.6ubuntu6.1                         all          Debian package development tools
ii  e2fsprogs                       1.47.0-2.4~exp1ubuntu4.1                amd64        ext2/ext3/ext4 file system utilities
ii  e2fsprogs-l10n                  1.47.0-2.4~exp1ubuntu4.1                all          ext2/ext3/ext4 file system utilities - translations
ii  eatmydata                       131-1ubuntu1                            all          Library and utilities designed to disable fsync and friends
ii  ed                              1.20.1-1                                amd64        classic UNIX line editor
ii  eject                           2.39.3-9ubuntu6.2                       amd64        ejects CDs and operates CD-Changers under Linux
ii  ethtool                         1:6.7-1build1                           amd64        display or change Ethernet device settings
ii  eza                             0.18.2-1                                amd64        Modern replacement for ls
ii  fakeroot                        1.33-1                                  amd64        tool for simulating superuser privileges
ii  fdisk                           2.39.3-9ubuntu6.2                       amd64        collection of partitioning utilities
ii  file                            1:5.45-3build1                          amd64        Recognize the type of data in a file using "magic" numbers
ii  findutils                       4.9.0-5build1                           amd64        utilities for finding files--find, xargs
ii  fontconfig                      2.15.0-1.1ubuntu2                       amd64        generic font configuration library - support binaries
ii  fontconfig-config               2.15.0-1.1ubuntu2                       amd64        generic font configuration library - configuration
ii  fonts-dejavu-core               2.37-8                                  all          Vera font family derivate with additional characters
ii  fonts-dejavu-mono               2.37-8                                  all          Vera font family derivate with additional characters
ii  fonts-lato                      2.015-1                                 all          sans-serif typeface family font
ii  fonts-ubuntu                    0.869+git20240321-0ubuntu1              all          sans-serif font set from Ubuntu
ii  fuse3                           3.14.0-5build1                          amd64        Filesystem in Userspace (3.x version)
ii  g++                             4:13.2.0-7ubuntu1                       amd64        GNU C++ compiler
ii  g++-13                          13.3.0-6ubuntu2~24.04                   amd64        GNU C++ compiler
ii  g++-13-x86-64-linux-gnu         13.3.0-6ubuntu2~24.04                   amd64        GNU C++ compiler for x86_64-linux-gnu architecture
ii  g++-x86-64-linux-gnu            4:13.2.0-7ubuntu1                       amd64        GNU C++ compiler for the amd64 architecture
ii  gawk                            1:5.2.1-2build3                         amd64        GNU awk, a pattern scanning and processing language
ii  gcc                             4:13.2.0-7ubuntu1                       amd64        GNU C compiler
ii  gcc-13                          13.3.0-6ubuntu2~24.04                   amd64        GNU C compiler
ii  gcc-13-base:amd64               13.3.0-6ubuntu2~24.04                   amd64        GCC, the GNU Compiler Collection (base package)
ii  gcc-13-x86-64-linux-gnu         13.3.0-6ubuntu2~24.04                   amd64        GNU C compiler for the x86_64-linux-gnu architecture
ii  gcc-14-base:amd64               14.2.0-4ubuntu2~24.04                   amd64        GCC, the GNU Compiler Collection (base package)
ii  gcc-x86-64-linux-gnu            4:13.2.0-7ubuntu1                       amd64        GNU C compiler for the amd64 architecture
ii  gdisk                           1.0.10-1build1                          amd64        GPT fdisk text-mode partitioning tool
ii  gettext-base                    0.21-14ubuntu2                          amd64        GNU Internationalization utilities for the base system
ii  gir1.2-girepository-2.0:amd64   1.80.1-1                                amd64        Introspection data for GIRepository library
ii  gir1.2-glib-2.0:amd64           2.80.0-6ubuntu3.4                       amd64        Introspection data for GLib, GObject, Gio and GModule
ii  gir1.2-packagekitglib-1.0       1.2.8-2ubuntu1.2                        amd64        GObject introspection data for the PackageKit GLib library
ii  git                             1:2.43.0-1ubuntu7.3                     amd64        fast, scalable, distributed revision control system
ii  git-man                         1:2.43.0-1ubuntu7.3                     all          fast, scalable, distributed revision control system (manual pages)
ii  gnupg                           2.4.4-2ubuntu17.3                       all          GNU privacy guard - a free PGP replacement
ii  gnupg-l10n                      2.4.4-2ubuntu17.3                       all          GNU privacy guard - localization files
ii  gnupg-utils                     2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - utility programs
ii  gpg                             2.4.4-2ubuntu17.3                       amd64        GNU Privacy Guard -- minimalist public key operations
ii  gpg-agent                       2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - cryptographic agent
ii  gpg-wks-client                  2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - Web Key Service client
ii  gpgconf                         2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - core configuration utilities
ii  gpgsm                           2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - S/MIME version
ii  gpgv                            2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - signature verification tool
ii  grep                            3.11-4build1                            amd64        GNU grep, egrep and fgrep
ii  groff-base                      1.23.0-3build2                          amd64        GNU troff text-formatting system (base system components)
ii  gsettings-desktop-schemas       46.1-0ubuntu1                           all          GSettings desktop-wide schemas
ii  gtk-update-icon-cache           3.24.41-4ubuntu1.3                      amd64        icon theme caching utility
ii  gzip                            1.12-1ubuntu3.1                         amd64        GNU compression utilities
ii  hicolor-icon-theme              0.17-2                                  all          default fallback theme for FreeDesktop.org icon themes
ii  hostname                        3.23+nmu2ubuntu2                        amd64        utility to set/show the host name or domain name
ii  humanity-icon-theme             0.6.16                                  all          Humanity Icon theme
ii  info                            7.1-3build2                             amd64        Standalone GNU Info documentation browser
ii  init                            1.66ubuntu1                             amd64        metapackage ensuring an init system is installed
ii  init-system-helpers             1.66ubuntu1                             all          helper tools for all init systems
ii  install-info                    7.1-3build2                             amd64        Manage installed documentation in info format
ii  iproute2                        6.1.0-1ubuntu6                          amd64        networking and traffic control tools
ii  iputils-ping                    3:20240117-1ubuntu0.1                   amd64        Tools to test the reachability of network hosts
ii  iso-codes                       4.16.0-1                                all          ISO language, territory, currency, script codes and their translations
ii  javascript-common               11+nmu1                                 all          Base support for JavaScript library packages
ii  jp2a                            1.1.1-2ubuntu2                          amd64        converts jpg and png images to ascii
ii  kbd                             2.6.4-2ubuntu2                          amd64        Linux console font and keytable utilities
ii  keyboard-configuration          1.226ubuntu1                            all          system-wide keyboard preferences
ii  keyboxd                         2.4.4-2ubuntu17.3                       amd64        GNU privacy guard - public key material service
ii  kmod                            31+20240202-2ubuntu7.1                  amd64        tools for managing Linux kernel modules
ii  krb5-locales                    1.20.1-6ubuntu2.6                       all          internationalization support for MIT Kerberos
ii  landscape-client                24.02-0ubuntu5.3                        amd64        Landscape administration system client
ii  landscape-common                24.02-0ubuntu5.3                        amd64        Landscape administration system client - Common files
ii  less                            590-2ubuntu2.1                          amd64        pager program similar to more
ii  libacl1:amd64                   2.3.2-1build1.1                         amd64        access control list - shared library
ii  libalgorithm-diff-perl          1.201-1                                 all          module to find differences between files
ii  libalgorithm-diff-xs-perl:amd64 0.04-8build3                            amd64        module to find differences between files (XS accelerated)
ii  libalgorithm-merge-perl         0.08-5                                  all          Perl module for three-way merge of textual data
ii  libaom3:amd64                   3.8.2-2ubuntu0.1                        amd64        AV1 Video Codec Library
ii  libapparmor1:amd64              4.0.1really4.0.1-0ubuntu0.24.04.4       amd64        changehat AppArmor library
ii  libappstream5:amd64             1.0.2-1build6                           amd64        Library to access AppStream services
ii  libapt-pkg6.0t64:amd64          2.8.3                                   amd64        package management runtime library
ii  libargon2-1:amd64               0~20190702+dfsg-4build1                 amd64        memory-hard hashing function - runtime library
ii  libasan8:amd64                  14.2.0-4ubuntu2~24.04                   amd64        AddressSanitizer -- a fast memory error detector
ii  libassuan0:amd64                2.5.6-1build1                           amd64        IPC library for the GnuPG components
ii  libatk-bridge2.0-0t64:amd64     2.52.0-1build1                          amd64        AT-SPI 2 toolkit bridge - shared library
ii  libatk1.0-0t64:amd64            2.52.0-1build1                          amd64        ATK accessibility toolkit
ii  libatm1t64:amd64                1:2.5.1-5.1build1                       amd64        shared library for ATM (Asynchronous Transfer Mode)
ii  libatomic1:amd64                14.2.0-4ubuntu2~24.04                   amd64        support library providing __atomic built-in functions
ii  libatspi2.0-0t64:amd64          2.52.0-1build1                          amd64        Assistive Technology Service Provider Interface - shared library
ii  libattr1:amd64                  1:2.5.2-1build1.1                       amd64        extended attribute handling - shared library
ii  libaudit-common                 1:3.1.2-2.1build1.1                     all          Dynamic library for security auditing - common files
ii  libaudit1:amd64                 1:3.1.2-2.1build1.1                     amd64        Dynamic library for security auditing
ii  libavahi-client3:amd64          0.8-13ubuntu6                           amd64        Avahi client library
ii  libavahi-common-data:amd64      0.8-13ubuntu6                           amd64        Avahi common data files
ii  libavahi-common3:amd64          0.8-13ubuntu6                           amd64        Avahi common library
ii  libbinutils:amd64               2.42-4ubuntu2.5                         amd64        GNU binary utilities (private shared library)
ii  libblkid1:amd64                 2.39.3-9ubuntu6.2                       amd64        block device ID library
ii  libbpf1:amd64                   1:1.3.0-2build2                         amd64        eBPF helper library (shared library)
ii  libbrotli1:amd64                1.1.0-2build2                           amd64        library implementing brotli encoder and decoder (shared libraries)
ii  libbsd0:amd64                   0.12.1-1build1.1                        amd64        utility functions from BSD systems - shared library
ii  libbz2-1.0:amd64                1.0.8-5.1build0.1                       amd64        high-quality block-sorting file compressor library - runtime
ii  libc-bin                        2.39-0ubuntu8.5                         amd64        GNU C Library: Binaries
ii  libc-dev-bin                    2.39-0ubuntu8.5                         amd64        GNU C Library: Development binaries
ii  libc-devtools                   2.39-0ubuntu8.5                         amd64        GNU C Library: Development tools
ii  libc6-dev:amd64                 2.39-0ubuntu8.5                         amd64        GNU C Library: Development Libraries and Header Files
ii  libc6:amd64                     2.39-0ubuntu8.5                         amd64        GNU C Library: Shared libraries
ii  libcairo-gobject2:amd64         1.18.0-3build1                          amd64        Cairo 2D vector graphics library (GObject library)
ii  libcairo2:amd64                 1.18.0-3build1                          amd64        Cairo 2D vector graphics library
ii  libcap-ng0:amd64                0.8.4-2build2                           amd64        alternate POSIX capabilities library
ii  libcap2-bin                     1:2.66-5ubuntu2.2                       amd64        POSIX 1003.1e capabilities (utilities)
ii  libcap2:amd64                   1:2.66-5ubuntu2.2                       amd64        POSIX 1003.1e capabilities (library)
ii  libcapstone-dev:amd64           4.0.2-5.1build1                         amd64        lightweight multi-architecture disassembly framework - devel files
ii  libcapstone4:amd64              4.0.2-5.1build1                         amd64        lightweight multi-architecture disassembly framework - library
ii  libcbor0.10:amd64               0.10.2-1.2ubuntu2                       amd64        library for parsing and generating CBOR (RFC 7049)
ii  libcc1-0:amd64                  14.2.0-4ubuntu2~24.04                   amd64        GCC cc1 plugin for GDB
ii  libcolord2:amd64                1.4.7-1build2                           amd64        system service to manage device colour profiles -- runtime
ii  libcom-err2:amd64               1.47.0-2.4~exp1ubuntu4.1                amd64        common error description library
ii  libcrypt-dev:amd64              1:4.4.36-4build1                        amd64        libcrypt development files
ii  libcrypt1:amd64                 1:4.4.36-4build1                        amd64        libcrypt shared library
ii  libcryptsetup12:amd64           2:2.7.0-1ubuntu4.2                      amd64        disk encryption support - shared library
ii  libctf-nobfd0:amd64             2.42-4ubuntu2.5                         amd64        Compact C Type Format library (runtime, no BFD dependency)
ii  libctf0:amd64                   2.42-4ubuntu2.5                         amd64        Compact C Type Format library (runtime, BFD dependency)
ii  libcups2t64:amd64               2.4.7-1.2ubuntu7.3                      amd64        Common UNIX Printing System(tm) - Core library
ii  libcurl3t64-gnutls:amd64        8.5.0-2ubuntu10.6                       amd64        easy-to-use client-side URL transfer library (GnuTLS flavour)
ii  libcurl4t64:amd64               8.5.0-2ubuntu10.6                       amd64        easy-to-use client-side URL transfer library (OpenSSL flavour)
ii  libdatrie1:amd64                0.2.13-3build1                          amd64        Double-array trie library
ii  libdb5.3t64:amd64               5.3.28+dfsg2-7                          amd64        Berkeley v5.3 Database Libraries [runtime]
ii  libdbus-1-3:amd64               1.14.10-4ubuntu4.1                      amd64        simple interprocess messaging system (library)
ii  libdconf1:amd64                 0.40.0-4build2                          amd64        simple configuration storage system - runtime library
ii  libde265-0:amd64                1.0.15-1build3                          amd64        Open H.265 video codec implementation
ii  libdebconfclient0:amd64         0.271ubuntu3                            amd64        Debian Configuration Management System (C-implementation library)
ii  libdeflate0:amd64               1.19-1build1.1                          amd64        fast, whole-buffer DEFLATE-based compression and decompression
ii  libdevmapper1.02.1:amd64        2:1.02.185-3ubuntu3.2                   amd64        Linux Kernel Device Mapper userspace library
ii  libdpkg-perl                    1.22.6ubuntu6.1                         all          Dpkg perl modules
ii  libdrm-amdgpu1:amd64            2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to amdgpu-specific kernel DRM services -- runtime
ii  libdrm-common                   2.4.122-1~ubuntu0.24.04.1               all          Userspace interface to kernel DRM services -- common files
ii  libdrm-intel1:amd64             2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to intel-specific kernel DRM services -- runtime
ii  libdrm-nouveau2:amd64           2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to nouveau-specific kernel DRM services -- runtime
ii  libdrm-radeon1:amd64            2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to radeon-specific kernel DRM services -- runtime
ii  libdrm2:amd64                   2.4.122-1~ubuntu0.24.04.1               amd64        Userspace interface to kernel DRM services -- runtime
ii  libduktape207:amd64             2.7.0+tests-0ubuntu3                    amd64        embeddable Javascript engine, library
ii  libdw1t64:amd64                 0.190-1.1ubuntu0.1                      amd64        library that provides access to the DWARF debug information
ii  libeatmydata1:amd64             131-1ubuntu1                            amd64        Library and utilities designed to disable fsync and friends - shared library     
ii  libedit2:amd64                  3.1-20230828-1build1                    amd64        BSD editline and history libraries
ii  libegl-mesa0:amd64              24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the EGL API -- Mesa vendor library
ii  libegl1:amd64                   1.7.0-1build1                           amd64        Vendor neutral GL dispatch library -- EGL support
ii  libelf1t64:amd64                0.190-1.1ubuntu0.1                      amd64        library to read and write ELF files
ii  libepoxy0:amd64                 1.5.10-1build1                          amd64        OpenGL function pointer management library
ii  liberror-perl                   0.17029-2                               all          Perl module for error/exception handling in an OO-ish way
ii  libestr0:amd64                  0.1.11-1build1                          amd64        Helper functions for handling strings (lib)
ii  libevent-core-2.1-7t64:amd64    2.1.12-stable-9ubuntu2                  amd64        Asynchronous event notification library (core)
ii  libexpat1:amd64                 2.6.1-2ubuntu0.3                        amd64        XML parsing C library - runtime library
ii  libext2fs2t64:amd64             1.47.0-2.4~exp1ubuntu4.1                amd64        ext2/ext3/ext4 file system libraries
ii  libfakeroot:amd64               1.33-1                                  amd64        tool for simulating superuser privileges - shared libraries
ii  libfastjson4:amd64              1.2304.0-1build1                        amd64        fast json library for C
ii  libfdisk1:amd64                 2.39.3-9ubuntu6.2                       amd64        fdisk partitioning library
ii  libffi8:amd64                   3.4.6-1build1                           amd64        Foreign Function Interface library runtime
ii  libfido2-1:amd64                1.14.0-1build3                          amd64        library for generating and verifying FIDO 2.0 objects
ii  libfile-fcntllock-perl          0.22-4ubuntu5                           amd64        Perl module for file locking with fcntl(2)
ii  libflac12t64:amd64              1.4.3+ds-2.1ubuntu2                     amd64        Free Lossless Audio Codec - runtime C library
ii  libfontconfig1:amd64            2.15.0-1.1ubuntu2                       amd64        generic font configuration library - runtime
ii  libfreetype6:amd64              2.13.2+dfsg-1build3                     amd64        FreeType 2 font engine, shared library files
ii  libfribidi0:amd64               1.0.13-3build1                          amd64        Free Implementation of the Unicode BiDi algorithm
ii  libfuse3-3:amd64                3.14.0-5build1                          amd64        Filesystem in Userspace (library) (3.x version)
ii  libgbm1:amd64                   24.2.8-1ubuntu1~24.04.1                 amd64        generic buffer management API -- runtime
ii  libgcc-13-dev:amd64             13.3.0-6ubuntu2~24.04                   amd64        GCC support library (development files)
ii  libgcc-s1:amd64                 14.2.0-4ubuntu2~24.04                   amd64        GCC support library
ii  libgcrypt20:amd64               1.10.3-2build1                          amd64        LGPL Crypto library - runtime library
ii  libgd3:amd64                    2.3.3-9ubuntu5                          amd64        GD Graphics Library
ii  libgdbm-compat4t64:amd64        1.23-5.1build1                          amd64        GNU dbm database routines (legacy support runtime version)
ii  libgdbm6t64:amd64               1.23-5.1build1                          amd64        GNU dbm database routines (runtime version)
ii  libgdk-pixbuf-2.0-0:amd64       2.42.10+dfsg-3ubuntu3.2                 amd64        GDK Pixbuf library
ii  libgdk-pixbuf2.0-bin            2.42.10+dfsg-3ubuntu3.2                 amd64        GDK Pixbuf library (thumbnailer)
ii  libgdk-pixbuf2.0-common         2.42.10+dfsg-3ubuntu3.2                 all          GDK Pixbuf library - data files
ii  libgirepository-1.0-1:amd64     1.80.1-1                                amd64        Library for handling GObject introspection data (runtime library)
ii  libgit2-1.7:amd64               1.7.2+ds-1ubuntu3                       amd64        low-level Git library
ii  libgl1-amber-dri:amd64          21.3.9-0ubuntu2                         amd64        free implementation of the OpenGL API -- Amber DRI modules
ii  libgl1-mesa-dri:amd64           24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the OpenGL API -- DRI modules
ii  libgl1:amd64                    1.7.0-1build1                           amd64        Vendor neutral GL dispatch library -- legacy GL support
ii  libglapi-mesa:amd64             24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the GL API -- shared library
ii  libglib2.0-0t64:amd64           2.80.0-6ubuntu3.4                       amd64        GLib library of C routines
ii  libglib2.0-bin                  2.80.0-6ubuntu3.4                       amd64        Programs for the GLib library
ii  libglib2.0-data                 2.80.0-6ubuntu3.4                       all          Common files for GLib library
ii  libglvnd0:amd64                 1.7.0-1build1                           amd64        Vendor neutral GL dispatch library
ii  libglx-mesa0:amd64              24.2.8-1ubuntu1~24.04.1                 amd64        free implementation of the OpenGL API -- GLX vendor library
ii  libglx0:amd64                   1.7.0-1build1                           amd64        Vendor neutral GL dispatch library -- GLX support
ii  libgmp10:amd64                  2:6.3.0+dfsg-2ubuntu6.1                 amd64        Multiprecision arithmetic library
ii  libgnutls30t64:amd64            3.8.3-1.1ubuntu3.4                      amd64        GNU TLS library - main runtime library
ii  libgomp1:amd64                  14.2.0-4ubuntu2~24.04                   amd64        GCC OpenMP (GOMP) support library
ii  libgpg-error-l10n               1.47-3build2.1                          all          library of error values and messages in GnuPG (localization files)
ii  libgpg-error0:amd64             1.47-3build2.1                          amd64        GnuPG development runtime library
ii  libgpm2:amd64                   1.20.7-11                               amd64        General Purpose Mouse - shared library
ii  libgprofng0:amd64               2.42-4ubuntu2.5                         amd64        GNU Next Generation profiler (runtime library)
ii  libgraphite2-3:amd64            1.3.14-2build1                          amd64        Font rendering engine for Complex Scripts -- library
ii  libgssapi-krb5-2:amd64          1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries - krb5 GSS-API Mechanism
ii  libgstreamer1.0-0:amd64         1.24.2-1ubuntu0.1                       amd64        Core GStreamer libraries and elements
ii  libgtk-3-0t64:amd64             3.24.41-4ubuntu1.3                      amd64        GTK graphical user interface library
ii  libgtk-3-bin                    3.24.41-4ubuntu1.3                      amd64        programs for the GTK graphical user interface library
ii  libgtk-3-common                 3.24.41-4ubuntu1.3                      all          common files for the GTK graphical user interface library
ii  libharfbuzz0b:amd64             8.3.0-2build2                           amd64        OpenType text shaping engine (shared library)
ii  libheif-plugin-aomdec:amd64     1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - aomdec plugin
ii  libheif-plugin-aomenc:amd64     1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - aomenc plugin
ii  libheif-plugin-libde265:amd64   1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - libde265 plugin
ii  libheif1:amd64                  1.17.6-1ubuntu4.1                       amd64        ISO/IEC 23008-12:2017 HEIF file format decoder - shared library
ii  libhogweed6t64:amd64            3.9.1-2.2build1.1                       amd64        low level cryptographic library (public-key cryptos)
ii  libhttp-parser2.9:amd64         2.9.4-6build1                           amd64        parser for HTTP messages written in C
ii  libhwasan0:amd64                14.2.0-4ubuntu2~24.04                   amd64        AddressSanitizer -- a fast memory error detector
ii  libicu74:amd64                  74.2-1ubuntu3.1                         amd64        International Components for Unicode
ii  libidn2-0:amd64                 2.3.7-2build1.1                         amd64        Internationalized domain names (IDNA2008/TR46) library
ii  libisl23:amd64                  0.26-3build1.1                          amd64        manipulating sets and relations of integer points bounded by linear constraints  
ii  libitm1:amd64                   14.2.0-4ubuntu2~24.04                   amd64        GNU Transactional Memory Library
ii  libjansson4:amd64               2.14-2build2                            amd64        C library for encoding, decoding and manipulating JSON data
ii  libjbig0:amd64                  2.1-6.1ubuntu2                          amd64        JBIGkit libraries
ii  libjpeg-turbo8:amd64            2.1.5-2ubuntu2                          amd64        libjpeg-turbo JPEG runtime library
ii  libjpeg8:amd64                  8c-2ubuntu11                            amd64        Independent JPEG Group's JPEG runtime library (dependency package)
ii  libjs-jquery                    3.6.1+dfsg+~3.5.14-1                    all          JavaScript library for dynamic web applications
ii  libjson-c5:amd64                0.17-1build1                            amd64        JSON manipulation library - shared library
ii  libk5crypto3:amd64              1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries - Crypto Library
ii  libkeyutils1:amd64              1.6.3-3build1                           amd64        Linux Key Management Utilities (library)
ii  libkmod2:amd64                  31+20240202-2ubuntu7.1                  amd64        libkmod shared library
ii  libkrb5-3:amd64                 1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries
ii  libkrb5support0:amd64           1.20.1-6ubuntu2.6                       amd64        MIT Kerberos runtime libraries - Support library
ii  libksba8:amd64                  1.6.6-1build1                           amd64        X.509 and CMS support library
ii  liblcms2-2:amd64                2.14-2build1                            amd64        Little CMS 2 color management library
ii  libldap-common                  2.6.7+dfsg-1~exp1ubuntu8.2              all          OpenLDAP common files for libraries
ii  libldap2:amd64                  2.6.7+dfsg-1~exp1ubuntu8.2              amd64        OpenLDAP libraries
ii  liblerc4:amd64                  4.0.0+ds-4ubuntu2                       amd64        Limited Error Raster Compression library
ii  libllvm19:amd64                 1:19.1.1-1ubuntu1~24.04.2               amd64        Modular compiler and toolchain technologies, runtime library
ii  liblocale-gettext-perl          1.07-6ubuntu5                           amd64        module using libc functions for internationalization in Perl
ii  liblsan0:amd64                  14.2.0-4ubuntu2~24.04                   amd64        LeakSanitizer -- a memory leak detector (runtime)
ii  liblz4-1:amd64                  1.9.4-1build1.1                         amd64        Fast LZ compression algorithm library - runtime
ii  liblz4-dev:amd64                1.9.4-1build1.1                         amd64        Fast LZ compression algorithm library - development files
ii  liblzma5:amd64                  5.6.1+really5.4.5-1ubuntu0.2            amd64        XZ-format compression library
ii  liblzo2-2:amd64                 2.10-2build4                            amd64        data compression library
ii  libmagic-dev:amd64              1:5.45-3build1                          amd64        Recognize the type of data in a file using "magic" numbers - development
ii  libmagic-mgc                    1:5.45-3build1                          amd64        File type determination library using "magic" numbers (compiled magic file)      
ii  libmagic1t64:amd64              1:5.45-3build1                          amd64        Recognize the type of data in a file using "magic" numbers - library
ii  libmd0:amd64                    1.1.0-2build1.1                         amd64        message digest functions from BSD systems - shared library
ii  libmnl0:amd64                   1.0.5-2build1                           amd64        minimalistic Netlink communication library
ii  libmount1:amd64                 2.39.3-9ubuntu6.2                       amd64        device mounting library
ii  libmp3lame0:amd64               3.100-6build1                           amd64        MP3 encoding library
ii  libmpc3:amd64                   1.3.1-1build1.1                         amd64        multiple precision complex floating-point library
ii  libmpfr6:amd64                  4.2.1-1build1.1                         amd64        multiple precision floating-point computation
ii  libmpg123-0t64:amd64            1.32.5-1ubuntu1.1                       amd64        MPEG layer 1/2/3 audio decoder (shared library)
ii  libncurses6:amd64               6.4+20240113-1ubuntu2                   amd64        shared libraries for terminal handling
ii  libncursesw6:amd64              6.4+20240113-1ubuntu2                   amd64        shared libraries for terminal handling (wide character support)
ii  libnetplan1:amd64               1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration runtime library
ii  libnettle8t64:amd64             3.9.1-2.2build1.1                       amd64        low level cryptographic library (symmetric and one-way cryptos)
ii  libnewt0.52:amd64               0.52.24-2ubuntu2                        amd64        Not Erik's Windowing Toolkit - text mode windowing with slang
ii  libnghttp2-14:amd64             1.59.0-1ubuntu0.2                       amd64        library implementing HTTP/2 protocol (shared library)
ii  libnpth0t64:amd64               1.6-3.1build1                           amd64        replacement for GNU Pth using system threads
ii  libnss-systemd:amd64            255.4-1ubuntu8.8                        amd64        nss module providing dynamic user and group name resolution
ii  libogg0:amd64                   1.3.5-3build1                           amd64        Ogg bitstream library
ii  libopus0:amd64                  1.4-1build1                             amd64        Opus codec runtime library
ii  libp11-kit0:amd64               0.25.3-4ubuntu2.1                       amd64        library for loading and coordinating access to PKCS#11 modules - runtime
ii  libpackagekit-glib2-18:amd64    1.2.8-2ubuntu1.2                        amd64        Library for accessing PackageKit using GLib
ii  libpam-cap:amd64                1:2.66-5ubuntu2.2                       amd64        POSIX 1003.1e capabilities (PAM module)
ii  libpam-modules-bin              1.5.3-5ubuntu5.4                        amd64        Pluggable Authentication Modules for PAM - helper binaries
ii  libpam-modules:amd64            1.5.3-5ubuntu5.4                        amd64        Pluggable Authentication Modules for PAM
ii  libpam-runtime                  1.5.3-5ubuntu5.4                        all          Runtime support for the PAM library
ii  libpam-systemd:amd64            255.4-1ubuntu8.8                        amd64        system and service manager - PAM module
ii  libpam0g:amd64                  1.5.3-5ubuntu5.4                        amd64        Pluggable Authentication Modules library
ii  libpango-1.0-0:amd64            1.52.1+ds-1build1                       amd64        Layout and rendering of internationalized text
ii  libpangocairo-1.0-0:amd64       1.52.1+ds-1build1                       amd64        Layout and rendering of internationalized text
ii  libpangoft2-1.0-0:amd64         1.52.1+ds-1build1                       amd64        Layout and rendering of internationalized text
ii  libpciaccess0:amd64             0.17-3ubuntu0.24.04.2                   amd64        Generic PCI access library for X
ii  libpcre2-8-0:amd64              10.42-4ubuntu2.1                        amd64        New Perl Compatible Regular Expression Library- 8 bit runtime files
ii  libperl5.38t64:amd64            5.38.2-3.2ubuntu0.2                     amd64        shared Perl library
ii  libpipeline1:amd64              1.5.7-2                                 amd64        Unix process pipeline manipulation library
ii  libpixman-1-0:amd64             0.42.2-1build1                          amd64        pixel-manipulation library for X and cairo
ii  libpng16-16t64:amd64            1.6.43-5build1                          amd64        PNG library - runtime (version 1.6)
ii  libpolkit-agent-1-0:amd64       124-2ubuntu1.24.04.2                    amd64        polkit Authentication Agent API
ii  libpolkit-gobject-1-0:amd64     124-2ubuntu1.24.04.2                    amd64        polkit Authorization API
ii  libpopt0:amd64                  1.19+dfsg-1build1                       amd64        lib for parsing cmdline parameters
ii  libproc2-0:amd64                2:4.0.4-4ubuntu3.2                      amd64        library for accessing process information from /proc
ii  libpsl5t64:amd64                0.21.2-1.1build1                        amd64        Library for Public Suffix List (shared libraries)
ii  libpython3-stdlib:amd64         3.12.3-0ubuntu2                         amd64        interactive high-level object-oriented language (default python3 version)        
ii  libpython3.12-minimal:amd64     3.12.3-1ubuntu0.7                       amd64        Minimal subset of the Python language (version 3.12)
ii  libpython3.12-stdlib:amd64      3.12.3-1ubuntu0.7                       amd64        Interactive high-level object-oriented language (standard library, version 3.12) 
ii  libpython3.12t64:amd64          3.12.3-1ubuntu0.7                       amd64        Shared Python runtime library (version 3.12)
ii  libquadmath0:amd64              14.2.0-4ubuntu2~24.04                   amd64        GCC Quad-Precision Math Library
ii  libradare2-5.0.0t64:amd64       5.5.0+dfsg-1.1ubuntu3                   amd64        libraries from the radare2 suite
ii  libradare2-common               5.5.0+dfsg-1.1ubuntu3                   all          arch independent files from the radare2 suite
ii  libradare2-dev                  5.5.0+dfsg-1.1ubuntu3                   amd64        devel files from the radare2 suite
ii  libreadline8t64:amd64           8.2-4build1                             amd64        GNU readline and history libraries, run-time libraries
ii  librsvg2-2:amd64                2.58.0+dfsg-1build1                     amd64        SAX-based renderer library for SVG files (runtime)
ii  librsvg2-common:amd64           2.58.0+dfsg-1build1                     amd64        SAX-based renderer library for SVG files (extra runtime)
ii  librtmp1:amd64                  2.4+20151223.gitfa8646d.1-2build7       amd64        toolkit for RTMP streams (shared library)
ii  libruby3.2:amd64                3.2.3-1ubuntu0.24.04.5                  amd64        Libraries necessary to run Ruby 3.2
ii  libruby:amd64                   1:3.2~ubuntu1                           amd64        Libraries necessary to run Ruby
ii  libsasl2-2:amd64                2.1.28+dfsg1-5ubuntu3.1                 amd64        Cyrus SASL - authentication abstraction library
ii  libsasl2-modules-db:amd64       2.1.28+dfsg1-5ubuntu3.1                 amd64        Cyrus SASL - pluggable authentication modules (DB)
ii  libsasl2-modules:amd64          2.1.28+dfsg1-5ubuntu3.1                 amd64        Cyrus SASL - pluggable authentication modules
ii  libseccomp2:amd64               2.5.5-1ubuntu3.1                        amd64        high level interface to Linux seccomp filter
ii  libselinux1:amd64               3.5-2ubuntu2.1                          amd64        SELinux runtime shared libraries
ii  libsemanage-common              3.5-1build5                             all          Common files for SELinux policy management libraries
ii  libsemanage2:amd64              3.5-1build5                             amd64        SELinux policy management library
ii  libsensors-config               1:3.6.0-9build1                         all          lm-sensors configuration files
ii  libsensors5:amd64               1:3.6.0-9build1                         amd64        library to read temperature/voltage/fan sensors
ii  libsepol2:amd64                 3.5-2build1                             amd64        SELinux library for manipulating binary security policies
ii  libsframe1:amd64                2.42-4ubuntu2.5                         amd64        Library to handle the SFrame format (runtime library)
ii  libsharpyuv0:amd64              1.3.2-0.4build3                         amd64        Library for sharp RGB to YUV conversion
ii  libsigsegv2:amd64               2.14-1ubuntu2                           amd64        Library for handling page faults in a portable way
ii  libslang2:amd64                 2.3.3-3build2                           amd64        S-Lang programming library - runtime version
ii  libsmartcols1:amd64             2.39.3-9ubuntu6.2                       amd64        smart column output alignment library
ii  libsndfile1:amd64               1.2.2-1ubuntu5.24.04.1                  amd64        Library for reading/writing audio files
ii  libsodium23:amd64               1.0.18-1build3                          amd64        Network communication, cryptography and signaturing library
ii  libsqlite3-0:amd64              3.45.1-1ubuntu2.4                       amd64        SQLite 3 shared library
ii  libss2:amd64                    1.47.0-2.4~exp1ubuntu4.1                amd64        command-line interface parsing library
ii  libssh-4:amd64                  0.10.6-2ubuntu0.1                       amd64        tiny C SSH library (OpenSSL flavor)
ii  libssh2-1t64:amd64              1.11.0-4.1build2                        amd64        SSH2 client-side library
ii  libssl3t64:amd64                3.0.13-0ubuntu3.5                       amd64        Secure Sockets Layer toolkit - shared libraries
ii  libstdc++-13-dev:amd64          13.3.0-6ubuntu2~24.04                   amd64        GNU Standard C++ Library v3 (development files)
ii  libstdc++6:amd64                14.2.0-4ubuntu2~24.04                   amd64        GNU Standard C++ Library v3
ii  libstemmer0d:amd64              2.2.0-4build1                           amd64        Snowball stemming algorithms for use in Information Retrieval
ii  libsystemd-shared:amd64         255.4-1ubuntu8.8                        amd64        systemd shared private library
ii  libsystemd0:amd64               255.4-1ubuntu8.8                        amd64        systemd utility library
ii  libtasn1-6:amd64                4.19.0-3ubuntu0.24.04.1                 amd64        Manage ASN.1 structures (runtime)
ii  libtext-charwidth-perl:amd64    0.04-11build3                           amd64        get display widths of characters on the terminal
ii  libtext-iconv-perl:amd64        1.7-8build3                             amd64        module to convert between character sets in Perl
ii  libtext-wrapi18n-perl           0.06-10                                 all          internationalized substitute of Text::Wrap
ii  libthai-data                    0.1.29-2build1                          all          Data files for Thai language support library
ii  libthai0:amd64                  0.1.29-2build1                          amd64        Thai language support library
ii  libtiff6:amd64                  4.5.1+git230720-4ubuntu2.2              amd64        Tag Image File Format (TIFF) library
ii  libtinfo6:amd64                 6.4+20240113-1ubuntu2                   amd64        shared low-level terminfo library for terminal handling
ii  libtirpc-common                 1.3.4+ds-1.1build1                      all          transport-independent RPC library - common files
ii  libtirpc3t64:amd64              1.3.4+ds-1.1build1                      amd64        transport-independent RPC library
ii  libtsan2:amd64                  14.2.0-4ubuntu2~24.04                   amd64        ThreadSanitizer -- a Valgrind-based detector of data races (runtime)
ii  libubsan1:amd64                 14.2.0-4ubuntu2~24.04                   amd64        UBSan -- undefined behaviour sanitizer (runtime)
ii  libuchardet0:amd64              0.0.8-1build1                           amd64        universal charset detection library - shared library
ii  libudev1:amd64                  255.4-1ubuntu8.8                        amd64        libudev shared library
ii  libunistring5:amd64             1.1-2build1.1                           amd64        Unicode string library for C
ii  libunwind8:amd64                1.6.2-3build1.1                         amd64        library to determine the call-chain of a program - runtime
ii  libutempter0:amd64              1.2.1-3build1                           amd64        privileged helper for utmp/wtmp updates (runtime)
ii  libuuid1:amd64                  2.39.3-9ubuntu6.2                       amd64        Universally Unique ID library
ii  libuv1-dev:amd64                1.48.0-1.1build1                        amd64        asynchronous event notification library - development files
ii  libuv1t64:amd64                 1.48.0-1.1build1                        amd64        asynchronous event notification library - runtime library
ii  libvorbis0a:amd64               1.3.7-1build3                           amd64        decoder library for Vorbis General Audio Compression Codec
ii  libvorbisenc2:amd64             1.3.7-1build3                           amd64        encoder library for Vorbis General Audio Compression Codec
ii  libvulkan1:amd64                1.3.275.0-1build1                       amd64        Vulkan loader library
ii  libwayland-client0:amd64        1.22.0-2.1build1                        amd64        wayland compositor infrastructure - client library
ii  libwayland-cursor0:amd64        1.22.0-2.1build1                        amd64        wayland compositor infrastructure - cursor library
ii  libwayland-egl1:amd64           1.22.0-2.1build1                        amd64        wayland compositor infrastructure - EGL library
ii  libwayland-server0:amd64        1.22.0-2.1build1                        amd64        wayland compositor infrastructure - server library
ii  libwebp7:amd64                  1.3.2-0.4build3                         amd64        Lossy compression of digital photographic images
ii  libx11-6:amd64                  2:1.8.7-1build1                         amd64        X11 client-side library
ii  libx11-data                     2:1.8.7-1build1                         all          X11 client-side library
ii  libx11-xcb1:amd64               2:1.8.7-1build1                         amd64        Xlib/XCB interface library
ii  libxau6:amd64                   1:1.0.9-1build6                         amd64        X11 authorisation library
ii  libxcb-dri2-0:amd64             1.15-1ubuntu2                           amd64        X C Binding, dri2 extension
ii  libxcb-dri3-0:amd64             1.15-1ubuntu2                           amd64        X C Binding, dri3 extension
ii  libxcb-glx0:amd64               1.15-1ubuntu2                           amd64        X C Binding, glx extension
ii  libxcb-present0:amd64           1.15-1ubuntu2                           amd64        X C Binding, present extension
ii  libxcb-randr0:amd64             1.15-1ubuntu2                           amd64        X C Binding, randr extension
ii  libxcb-render0:amd64            1.15-1ubuntu2                           amd64        X C Binding, render extension
ii  libxcb-shm0:amd64               1.15-1ubuntu2                           amd64        X C Binding, shm extension
ii  libxcb-sync1:amd64              1.15-1ubuntu2                           amd64        X C Binding, sync extension
ii  libxcb-xfixes0:amd64            1.15-1ubuntu2                           amd64        X C Binding, xfixes extension
ii  libxcb1:amd64                   1.15-1ubuntu2                           amd64        X C Binding
ii  libxcomposite1:amd64            1:0.4.5-1build3                         amd64        X11 Composite extension library
ii  libxcursor1:amd64               1:1.2.1-1build1                         amd64        X cursor management library
ii  libxdamage1:amd64               1:1.1.6-1build1                         amd64        X11 damaged region extension library
ii  libxdmcp6:amd64                 1:1.1.3-0ubuntu6                        amd64        X11 Display Manager Control Protocol library
ii  libxext6:amd64                  2:1.3.4-1build2                         amd64        X11 miscellaneous extension library
ii  libxfixes3:amd64                1:6.0.0-2build1                         amd64        X11 miscellaneous 'fixes' extension library
ii  libxi6:amd64                    2:1.8.1-1build1                         amd64        X11 Input extension library
ii  libxinerama1:amd64              2:1.1.4-3build1                         amd64        X11 Xinerama extension library
ii  libxkbcommon0:amd64             1.6.0-1build1                           amd64        library interface to the XKB compiler - shared library
ii  libxml2:amd64                   2.9.14+dfsg-1.3ubuntu3.3                amd64        GNOME XML library
ii  libxmlb2:amd64                  0.3.18-1                                amd64        Binary XML library
ii  libxmuu1:amd64                  2:1.1.3-3build2                         amd64        X11 miscellaneous micro-utility library
ii  libxpm4:amd64                   1:3.5.17-1build2                        amd64        X11 pixmap library
ii  libxrandr2:amd64                2:1.5.2-2build1                         amd64        X11 RandR extension library
ii  libxrender1:amd64               1:0.9.10-1.1build1                      amd64        X Rendering Extension client library
ii  libxshmfence1:amd64             1.3-1build5                             amd64        X shared memory fences - shared library
ii  libxtables12:amd64              1.8.10-3ubuntu2                         amd64        netfilter xtables library
ii  libxtst6:amd64                  2:1.2.3-1.1build1                       amd64        X11 Testing -- Record extension library
ii  libxxf86vm1:amd64               1:1.1.4-1build4                         amd64        X11 XFree86 video mode extension library
ii  libxxhash0:amd64                0.8.2-2build1                           amd64        shared library for xxhash
ii  libyaml-0-2:amd64               0.2.5-1build1                           amd64        Fast YAML 1.1 parser and emitter library
ii  libzip-dev:amd64                1.7.3-1.1ubuntu2                        amd64        library for reading, creating, and modifying zip archives (development)
ii  libzip4t64:amd64                1.7.3-1.1ubuntu2                        amd64        library for reading, creating, and modifying zip archives (runtime)
ii  libzstd1:amd64                  1.5.5+dfsg2-2build1.1                   amd64        fast lossless compression algorithm
ii  linux-libc-dev:amd64            6.8.0-71.71                             amd64        Linux Kernel Headers for development
ii  locales                         2.39-0ubuntu8.5                         all          GNU C Library: National Language (locale) data [support]
ii  login                           1:4.13+dfsg1-4ubuntu3.2                 amd64        system login tools
ii  logrotate                       3.21.0-2build1                          amd64        Log rotation utility
ii  logsave                         1.47.0-2.4~exp1ubuntu4.1                amd64        save the output of a command in a log file
ii  lolcat                          100.0.1-3                               all          colorful `cat`
ii  lsb-release                     12.0-2                                  all          Linux Standard Base version reporting utility (minimal implementation)
ii  lshw                            02.19.git.2021.06.19.996aaad9c7-2build3 amd64        information about hardware configuration
ii  lsof                            4.95.0-1build3                          amd64        utility to list open files
ii  lto-disabled-list               47                                      all          list of packages not to build with LTO
ii  make                            4.3-4.1build2                           amd64        utility for directing compilation
ii  man-db                          2.12.0-4build2                          amd64        tools for reading manual pages
ii  manpages                        6.7-2                                   all          Manual pages about using a GNU/Linux system
ii  manpages-dev                    6.7-2                                   all          Manual pages about using GNU/Linux for development
ii  mawk                            1.3.4.20240123-1build1                  amd64        Pattern scanning and text processing language
ii  media-types                     10.1.0                                  all          List of standard media types and their usual file extension
ii  mesa-libgallium:amd64           24.2.8-1ubuntu1~24.04.1                 amd64        shared infrastructure for Mesa drivers
ii  mesa-vulkan-drivers:amd64       24.2.8-1ubuntu1~24.04.1                 amd64        Mesa Vulkan graphics drivers
ii  motd-news-config                13ubuntu10.2                            all          Configuration for motd-news shipped in base-files
ii  mount                           2.39.3-9ubuntu6.2                       amd64        tools for mounting and manipulating filesystems
ii  nano                            7.2-2ubuntu0.1                          amd64        small, friendly text editor inspired by Pico
ii  ncurses-base                    6.4+20240113-1ubuntu2                   all          basic terminal type definitions
ii  ncurses-bin                     6.4+20240113-1ubuntu2                   amd64        terminal-related programs and man pages
ii  netbase                         6.4                                     all          Basic TCP/IP networking system
ii  netcat-openbsd                  1.226-1ubuntu2                          amd64        TCP/IP swiss army knife
ii  netplan-generator               1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration for various backends at boot
ii  netplan.io                      1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration for various backends at runtime
ii  networkd-dispatcher             2.2.4-1                                 all          Dispatcher service for systemd-networkd connection status changes
ii  openssh-client                  1:9.6p1-3ubuntu13.12                    amd64        secure shell (SSH) client, for secure access to remote machines
ii  openssl                         3.0.13-0ubuntu3.5                       amd64        Secure Sockets Layer toolkit - cryptographic utility
ii  packagekit                      1.2.8-2ubuntu1.2                        amd64        Provides a package management service
ii  packagekit-tools                1.2.8-2ubuntu1.2                        amd64        Provides PackageKit command-line tools
ii  passwd                          1:4.13+dfsg1-4ubuntu3.2                 amd64        change and administer password and group data
ii  pastebinit                      1.6.2-1                                 all          command-line pastebin client
ii  patch                           2.7.6-7build3                           amd64        Apply a diff file to an original
ii  pci.ids                         0.0~2024.03.31-1ubuntu0.1               all          PCI ID Repository
ii  perl                            5.38.2-3.2ubuntu0.2                     amd64        Larry Wall's Practical Extraction and Report Language
ii  perl-base                       5.38.2-3.2ubuntu0.2                     amd64        minimal Perl system
ii  perl-modules-5.38               5.38.2-3.2ubuntu0.2                     all          Core Perl modules
ii  pinentry-curses                 1.2.1-3ubuntu5                          amd64        curses-based PIN or pass-phrase entry dialog for GnuPG
ii  polkitd                         124-2ubuntu1.24.04.2                    amd64        framework for managing administrative policies and privileges
ii  procps                          2:4.0.4-4ubuntu3.2                      amd64        /proc file system utilities
ii  psmisc                          23.7-1build1                            amd64        utilities that use the proc file system
ii  publicsuffix                    20231001.0357-0.1                       all          accurate, machine-readable list of domain name suffixes
ii  python-apt-common               2.7.7ubuntu4                            all          Python interface to libapt-pkg (locales)
ii  python-babel-localedata         2.10.3-3build1                          all          tools for internationalizing Python applications - locale data files
ii  python3                         3.12.3-0ubuntu2                         amd64        interactive high-level object-oriented language (default python3 version)        
ii  python3-apport                  2.28.1-0ubuntu3.8                       all          Python 3 library for Apport crash report handling
ii  python3-apt                     2.7.7ubuntu4                            amd64        Python 3 interface to libapt-pkg
ii  python3-attr                    23.2.0-2                                all          Attributes without boilerplate (Python 3)
ii  python3-automat                 22.10.0-2                               all          Self-service finite-state machines for the programmer on the go
ii  python3-babel                   2.10.3-3build1                          all          tools for internationalizing Python applications - Python 3.x
ii  python3-bcrypt                  3.2.2-1build1                           amd64        password hashing library for Python 3
ii  python3-blinker                 1.7.0-1                                 all          Fast, simple object-to-object and broadcast signaling (Python3)
ii  python3-certifi                 2023.11.17-1                            all          root certificates for validating SSL certs and verifying TLS hosts (python3)     
ii  python3-cffi-backend:amd64      1.16.0-2build1                          amd64        Foreign Function Interface for Python 3 calling C code - runtime
ii  python3-chardet                 5.2.0+dfsg-1                            all          Universal Character Encoding Detector (Python3)
ii  python3-click                   8.1.6-2                                 all          Wrapper around optparse for command line utilities - Python 3.x
ii  python3-colorama                0.4.6-4                                 all          Cross-platform colored terminal text in Python - Python 3.x
ii  python3-commandnotfound         23.04.0                                 all          Python 3 bindings for command-not-found.
ii  python3-configobj               5.0.8-3                                 all          simple but powerful config file reader and writer for Python 3
ii  python3-constantly              23.10.4-1                               all          Symbolic constants in Python
ii  python3-cryptography            41.0.7-4ubuntu0.1                       amd64        Python library exposing cryptographic recipes and primitives (Python 3)
ii  python3-dbus                    1.3.2-5build3                           amd64        simple interprocess messaging system (Python 3 interface)
ii  python3-debconf                 1.5.86ubuntu1                           all          interact with debconf from Python 3
ii  python3-distro                  1.9.0-1                                 all          Linux OS platform information API
ii  python3-distro-info             1.7build1                               all          information about distributions' releases (Python 3 module)
ii  python3-distupgrade             1:24.04.26                              all          manage release upgrades
ii  python3-gdbm:amd64              3.12.3-0ubuntu1                         amd64        GNU dbm database support for Python 3.x
ii  python3-gi                      3.48.2-1                                amd64        Python 3 bindings for gobject-introspection libraries
ii  python3-hamcrest                2.1.0-1                                 all          Hamcrest framework for matcher objects (Python 3)
ii  python3-httplib2                0.20.4-3                                all          comprehensive HTTP client library written for Python3
ii  python3-hyperlink               21.0.0-5                                all          Immutable, Pythonic, correct URLs.
ii  python3-idna                    3.6-2ubuntu0.1                          all          Python IDNA2008 (RFC 5891) handling (Python 3)
ii  python3-incremental             22.10.0-1                               all          Library for versioning Python projects
ii  python3-jinja2                  3.1.2-1ubuntu1.3                        all          small but fast and easy to use stand-alone template engine
ii  python3-json-pointer            2.0-0ubuntu1                            all          resolve JSON pointers - Python 3.x
ii  python3-jsonpatch               1.32-3                                  all          library to apply JSON patches - Python 3.x
ii  python3-jsonschema              4.10.3-2ubuntu1                         all          An(other) implementation of JSON Schema (Draft 3, 4, 6, 7)
ii  python3-jwt                     2.7.0-1                                 all          Python 3 implementation of JSON Web Token
ii  python3-launchpadlib            1.11.0-6                                all          Launchpad web services client library (Python 3)
ii  python3-lazr.restfulclient      0.14.6-1                                all          client for lazr.restful-based web services (Python 3)
ii  python3-lazr.uri                1.0.6-3                                 all          library for parsing, manipulating, and generating URIs
ii  python3-markdown-it             3.0.0-2                                 all          Python port of markdown-it and some its associated plugins
ii  python3-markupsafe              2.1.5-1build2                           amd64        HTML/XHTML/XML string library
ii  python3-mdurl                   0.1.2-1                                 all          Python port of the JavaScript mdurl package
ii  python3-minimal                 3.12.3-0ubuntu2                         amd64        minimal subset of the Python language (default python3 version)
ii  python3-netifaces:amd64         0.11.0-2build3                          amd64        portable network interface information - Python 3.x
ii  python3-netplan                 1.1.2-2~ubuntu24.04.1                   amd64        Declarative network configuration Python bindings
ii  python3-newt:amd64              0.52.24-2ubuntu2                        amd64        NEWT module for Python3
ii  python3-oauthlib                3.2.2-1                                 all          generic, spec-compliant implementation of OAuth for Python3
ii  python3-openssl                 23.2.0-1                                all          Python 3 wrapper around the OpenSSL library
ii  python3-pkg-resources           68.1.2-2ubuntu1.2                       all          Package Discovery and Resource Access using pkg_resources
ii  python3-problem-report          2.28.1-0ubuntu3.8                       all          Python 3 library to handle problem reports
ii  python3-pyasn1                  0.4.8-4                                 all          ASN.1 library for Python (Python 3 module)
ii  python3-pyasn1-modules          0.2.8-1                                 all          Collection of protocols modules written in ASN.1 language (Python 3)
ii  python3-pycurl                  7.45.3-1build2                          amd64        Python bindings to libcurl (Python 3)
ii  python3-pygments                2.17.2+dfsg-1                           all          syntax highlighting package written in Python 3
ii  python3-pyparsing               3.1.1-1                                 all          alternative to creating and executing simple grammars - Python 3.x
ii  python3-pyrsistent:amd64        0.20.0-1build2                          amd64        persistent/functional/immutable data structures for Python
ii  python3-requests                2.31.0+dfsg-1ubuntu1.1                  all          elegant and simple HTTP library for Python3, built for human beings
ii  python3-rich                    13.7.1-1                                all          render rich text, tables, progress bars, syntax highlighting, markdown and more  
ii  python3-serial                  3.5-2                                   all          pyserial - module encapsulating access for the serial port
ii  python3-service-identity        24.1.0-1                                all          Service identity verification for pyOpenSSL (Python 3 module)
ii  python3-setuptools              68.1.2-2ubuntu1.2                       all          Python3 Distutils Enhancements
ii  python3-six                     1.16.0-4                                all          Python 2 and 3 compatibility library
ii  python3-software-properties     0.99.49.2                               all          manage the repositories that you install software from
ii  python3-systemd                 235-1build4                             amd64        Python 3 bindings for systemd
ii  python3-twisted                 24.3.0-1ubuntu0.1                       all          Event-based framework for internet applications
ii  python3-typing-extensions       4.10.0-1                                all          Backported and Experimental Type Hints for Python
ii  python3-tz                      2024.1-2                                all          Python3 version of the Olson timezone database
ii  python3-update-manager          1:24.04.12                              all          Python 3.x module for update-manager
ii  python3-urllib3                 2.0.7-1ubuntu0.2                        all          HTTP library with thread-safe connection pooling for Python3
ii  python3-wadllib                 1.3.6-5                                 all          Python 3 library for navigating WADL files
ii  python3-yaml                    6.0.1-2build2                           amd64        YAML parser and emitter for Python3
ii  python3-zope.interface          6.1-1build1                             amd64        Interfaces for Python3
ii  python3.12                      3.12.3-1ubuntu0.7                       amd64        Interactive high-level object-oriented language (version 3.12)
ii  python3.12-minimal              3.12.3-1ubuntu0.7                       amd64        Minimal subset of the Python language (version 3.12)
ii  radare2                         5.5.0+dfsg-1.1ubuntu3                   amd64        free and advanced command line hexadecimal editor
ii  rake                            13.0.6-3                                all          ruby make-like utility
ii  readline-common                 8.2-4build1                             all          GNU readline and history libraries, common files
ii  rpcsvc-proto                    1.4.2-0ubuntu7                          amd64        RPC protocol compiler and definitions
ii  rsync                           3.2.7-1ubuntu1.2                        amd64        fast, versatile, remote (and local) file-copying tool
ii  rsyslog                         8.2312.0-3ubuntu9.1                     amd64        reliable system and kernel logging daemon
ii  ruby                            1:3.2~ubuntu1                           amd64        Interpreter of object-oriented scripting language Ruby (default version)
ii  ruby-net-telnet                 0.2.0-1                                 all          telnet client library
ii  ruby-optimist                   3.0.0-2                                 all          Commandline parser for Ruby that just gets out of your way
ii  ruby-paint                      2.2.0-1                                 all          terminal paint library with 256 color and effect support
ii  ruby-rubygems                   3.4.20-1                                all          Package management framework for Ruby
ii  ruby-sdbm:amd64                 1.0.0-5build4                           amd64        simple file-based key-value store with String keys and values
ii  ruby-webrick                    1.8.1-1ubuntu0.1                        all          HTTP server toolkit in Ruby
ii  ruby-xmlrpc                     0.3.2-2                                 all          XMLRPC library for Ruby
ii  ruby3.2                         3.2.3-1ubuntu0.24.04.5                  amd64        Interpreter of object-oriented scripting language Ruby
ii  rubygems-integration            1.18                                    all          integration of Debian Ruby packages with Rubygems
ii  run-one                         1.17-0ubuntu2                           all          run just one instance of a command and its args at a time
ii  sed                             4.9-2build1                             amd64        GNU stream editor for filtering/transforming text
ii  sensible-utils                  0.0.22                                  all          Utilities for sensible alternative selection
ii  session-migration               0.3.9build1                             amd64        Tool to migrate in user session settings
ii  sgml-base                       1.31                                    all          SGML infrastructure and SGML catalog file support
ii  shared-mime-info                2.4-4                                   amd64        FreeDesktop.org shared MIME database and spec
ii  show-motd                       3.12                                    all          show message of the day in interactive shells
ii  snapd                           2.67.1+24.04                            amd64        Daemon and tooling that enable snap packages
ii  software-properties-common      0.99.49.2                               all          manage the repositories that you install software from (common)
ii  squashfs-tools                  1:4.6.1-1build1                         amd64        Tool to create and append to squashfs filesystems
ii  sudo                            1.9.15p5-3ubuntu5.24.04.1               amd64        Provide limited super user privileges to specific users
ii  systemd                         255.4-1ubuntu8.8                        amd64        system and service manager
ii  systemd-dev                     255.4-1ubuntu8.8                        all          systemd development files
ii  systemd-hwe-hwdb                255.1.4                                 all          udev rules for hardware enablement (HWE)
ii  systemd-resolved                255.4-1ubuntu8.8                        amd64        systemd DNS resolver
ii  systemd-sysv                    255.4-1ubuntu8.8                        amd64        system and service manager - SysV compatibility symlinks
ii  systemd-timesyncd               255.4-1ubuntu8.8                        amd64        minimalistic service to synchronize local time with NTP servers
ii  sysvinit-utils                  3.08-6ubuntu3                           amd64        System-V-like utilities
ii  tar                             1.35+dfsg-3build1                       amd64        GNU version of the tar archiving utility
ii  time                            1.9-0.2build1                           amd64        GNU time program for measuring CPU resource usage
ii  tmux                            3.4-1ubuntu0.1                          amd64        terminal multiplexer
ii  tzdata                          2025b-0ubuntu0.24.04.1                  all          time zone and daylight-saving time data
ii  ubuntu-keyring                  2023.11.28.1                            all          GnuPG keys of the Ubuntu archive
ii  ubuntu-minimal                  1.539.2                                 amd64        Minimal core of Ubuntu
ii  ubuntu-mono                     24.04-0ubuntu1                          all          Ubuntu Mono Icon theme
ii  ubuntu-pro-client               35.1ubuntu0~24.04                       amd64        Management tools for Ubuntu Pro
ii  ubuntu-pro-client-l10n          35.1ubuntu0~24.04                       amd64        Translations for Ubuntu Pro Client
ii  ubuntu-release-upgrader-core    1:24.04.26                              all          manage release upgrades
ii  ubuntu-wsl                      1.539.2                                 amd64        Ubuntu on Windows tools - Windows Subsystem for Linux integration
ii  ucf                             3.0043+nmu1                             all          Update Configuration File(s): preserve user changes to config files
ii  udev                            255.4-1ubuntu8.8                        amd64        /dev/ and hotplug management daemon
ii  unattended-upgrades             2.9.1+nmu4ubuntu1                       all          automatic installation of security upgrades
ii  unzip                           6.0-28ubuntu4.1                         amd64        De-archiver for .zip files
ii  update-manager-core             1:24.04.12                              all          manage release upgrades
ii  update-motd                     3.12                                    all          complements pam_motd in libpam-modules
ii  usb.ids                         2024.03.18-1                            all          USB ID Repository
ii  util-linux                      2.39.3-9ubuntu6.2                       amd64        miscellaneous system utilities
ii  uuid-runtime                    2.39.3-9ubuntu6.2                       amd64        runtime components for the Universally Unique ID library
ii  vim                             2:9.1.0016-1ubuntu7.8                   amd64        Vi IMproved - enhanced vi editor
ii  vim-common                      2:9.1.0016-1ubuntu7.8                   all          Vi IMproved - Common files
ii  vim-runtime                     2:9.1.0016-1ubuntu7.8                   all          Vi IMproved - Runtime files
ii  vim-tiny                        2:9.1.0016-1ubuntu7.8                   amd64        Vi IMproved - enhanced vi editor - compact version
ii  wget                            1.21.4-1ubuntu4.1                       amd64        retrieves files from the web
ii  whiptail                        0.52.24-2ubuntu2                        amd64        Displays user-friendly dialog boxes from shell scripts
ii  wsl-pro-service                 0.1.4                                   amd64        Ubuntu Pro for WSL - WSL service
ii  wsl-setup                       0.5.8~24.04                             amd64        WSL integration setup
ii  x11-common                      1:7.7+23ubuntu3                         all          X Window System (X.Org) infrastructure
ii  xauth                           1:1.1.2-1build1                         amd64        X authentication utility
ii  xdg-user-dirs                   0.18-1build1                            amd64        tool to manage well known user directories
ii  xkb-data                        2.41-2ubuntu1.1                         all          X Keyboard Extension (XKB) configuration data
ii  xml-core                        0.19                                    all          XML infrastructure and XML catalog file support
ii  xxd                             2:9.1.0016-1ubuntu7.8                   amd64        tool to make (or reverse) a hex dump
ii  xz-utils                        5.6.1+really5.4.5-1ubuntu0.2            amd64        XZ-format compression utilities
ii  zip                             3.0-13ubuntu0.2                         amd64        Archiver for .zip files
ii  zlib1g-dev:amd64                1:1.3.dfsg-3.1ubuntu2.1                 amd64        compression library - development
ii  zlib1g:amd64                    1:1.3.dfsg-3.1ubuntu2.1                 amd64        compression library - runtime
ii  zsh                             5.9-6ubuntu2                            amd64        shell with lots of features
ii  zsh-common                      5.9-6ubuntu2                            all          architecture independent files for Zsh
PS C:\AI\api>
PS C:\AI\api> winget list
名称                                                     ID                                                       版本                     可用                    源
-------------------------------------------------------------------------------------------------------------------------------------------------------------------------
NVM for Windows 1.2.2                                    CoreyButler.NVMforWindows                                1.2.2                                            winget
Cherry Studio                                            kangfenmao.CherryStudio                                  1.4.7                    1.5.6                   winget
CPUID CPU-Z 2.15                                         CPUID.CPU-Z                                              2.15                     2.16                    winget
Docker Desktop                                           Docker.DockerDesktop                                     4.41.2                   4.44.1                  winget
Everything 1.4.1.1026 (x64)                              ARP\Machine\X64\Everything                               1.4.1.1026
Git                                                      Git.Git                                                  2.48.1                   2.50.1                  winget
金舟录屏大师                                             ARP\Machine\X64\JZRecord                                 4.1.0.0
微软OfficePLUS                                           ARP\Machine\X64\MSOfficePLUS                             3.11.0.36530
PremiumSoft Navicat Premium 17.1                         PremiumSoft.NavicatPremium                               17.1.12                  17.3.2                  winget
Notepad++ (64-bit x64)                                   Notepad++.Notepad++                                      8.7.7                    8.8.5                   winget
Microsoft OneDrive                                       Microsoft.OneDrive                                       25.137.0715.0001                                 winget
Oracle VirtualBox Guest Additions 7.1.10                 ARP\Machine\X64\Oracle VirtualBox Guest Additions        7.1.10.169112
Microsoft Office 专业增强版 2024 - zh-cn                 ARP\Machine\X64\ProPlus2024Retail - zh-cn                16.0.19029.20156
VLC media player                                         ARP\Machine\X64\VLC media player                         3.0.21
WinRAR 7.00 (64-位)                                      RARLab.WinRAR                                            7.00.0                   7.13.0                  winget
Chatbox 1.14.3                                           Bin-Huang.Chatbox                                        1.14.3                   1.15.4                  winget
LM Studio 0.3.15                                         ElementLabs.LMStudio                                     0.3.15                   0.3.23                  winget
Apifox 2.7.2                                             Ruihu.Apifox                                             2.7.2                    2.7.29                  winget
AdsPower Global 7.7.18                                   AdsPower.AdsPower                                        7.7.18                                           winget
Mihomo Party                                             ARP\Machine\X64\dca524c3-c4d3-54b7-891e-e781dd58e37f     1.7.7
VirtualBrowser 2.2.2                                     ARP\Machine\X64\e80ab361-cee4-57d5-9ad7-8a316ce16d68     2.2.2
MQTTX 1.11.1                                             EMQ.MQTTX                                                1.11.1                   1.12.0                  winget
Microsoft Visual C++ 2010  x64 Redistributable - 10.0.4… Microsoft.VCRedist.2010.x64                              10.0.40219                                       winget
NVIDIA Nsight Compute 2025.1.1                           ARP\Machine\X64\{2BB28A64-B26D-4C91-BEBA-86048EFCE146}   25.1.1.0
Apple Mobile Device Support                              Apple.AppleMobileDeviceSupport                           18.5.0.13                                        winget
Inkscape                                                 Inkscape.Inkscape                                        1.4.0                    1.4.2                   winget 
Bonjour                                                  Apple.Bonjour                                            3.1.0.1                                          winget 
Microsoft Visual C++ 2008 Redistributable - x64 9.0.307… Microsoft.VCRedist.2008.x64                              9.0.30729.6161                                   winget 
MySQL Connector/ODBC 9.3 (64-bit)                        ARP\Machine\X64\{63737C32-46CE-427D-91A7-3B51D3BE48C1}   9.3.0
Pandoc 3.7.0.2                                           JohnMacFarlane.Pandoc                                    3.7.0.2                                          winget 
Microsoft Visual Studio Installer                        ARP\Machine\X64\{6F320B93-EE3C-4826-85E0-ADF79F8D4C61}   3.13.2069.59209
NVIDIA Nsight Systems 2024.6.2                           ARP\Machine\X64\{7BE9431F-8C18-4E5A-990A-243DD2C555A7}   24.6.2.225
ScreenToGif                                              NickeManarin.ScreenToGif                                 2.41.3                   2.41.5                  winget 
Autodesk SketchBook                                      ARP\Machine\X64\{AE6C5657-8710-4968-BEB5-1E2ED89CB2D2}   8.71.0000
NVIDIA CUDA Toolkit 12.8                                 Nvidia.CUDA                                              12.8                     13                      winget 
NVIDIA 图形驱动程序 576.52                               ARP\Machine\X64\{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_… 576.52
NVIDIA 应用程序 11.0.3.241                               ARP\Machine\X64\{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_… 11.0.3.241
NVIDIA PhysX 系统软件 9.23.1019                          ARP\Machine\X64\{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_… 9.23.1019
NVIDIA HD 音频驱动程序 1.4.3.2                           ARP\Machine\X64\{B2FE1952-0186-46C3-BAEC-A80AA35AC5B8}_… 1.4.3.2
NVIDIA Tools Extension SDK (NVTX) - 64 bit               ARP\Machine\X64\{B56D2F88-8865-40FD-B7AC-F074EE4D201D}   1.00.00.00
Node.js                                                  OpenJS.NodeJS                                            24.5.0                   24.6.0                  winget 
Oracle VirtualBox 7.1.10                                 Oracle.VirtualBox                                        7.1.10                   7.2.0                   winget 
NVIDIA Nsight Visual Studio Edition 2025.1.0.25055       ARP\Machine\X64\{E4CF044F-B97F-4DC4-AE8B-487CD13BCFC2}   25.1.0.25055
夸克网盘                                                 Alibaba.Quark                                            4.3.2.475                                        winget 
DeskPins                                                 EliasFotinis.DeskPins                                    1.32                                             winget 
钉钉                                                     Alibaba.DingTalk                                         7.6.55-Release.250402005 7.8.5-Release.250710001 winget 
Google Chrome                                            Google.Chrome.EXE                                        139.0.7258.67            139.0.7258.128          winget 
IntelliJ IDEA 2024.3.4.1                                 JetBrains.IntelliJIDEA.Ultimate                          2024.3.4.1               2025.2                  winget 
Microsoft Edge                                           Microsoft.Edge                                           139.0.3405.86                                    winget 
NetSetMan 5.4.0                                          NetSetMan.NetSetMan                                      5.4.0                                            winget 
Adobe Photoshop 2025                                     ARP\Machine\X86\PHSP_26_0                                26.0.0.26
QQ音乐                                                   Tencent.QQMusic                                          21.51                    21.71                   winget 
腾讯电脑管家                                             ARP\Machine\X86\QQPCMgr                                  17.8.27854.209
ToDesk                                                   Youqu.ToDesk                                             4.7.6.3                                          winget 
UXP WebView Support                                      ARP\Machine\X86\UXPW_1_2_0                               1.2.0
微信                                                     Tencent.WeChat.Universal                                 4.0.6.26                 4.1.0.14                winget 
ZeroTier One                                             ZeroTier.ZeroTierOne                                     1.14.2                                           winget 
Visual Studio 生成工具 2022                              ARP\Machine\X86\d16b320d                                 17.13.3
爱思助手8.0                                              ARP\Machine\X86\i4Tools8_x64                             8.36.016
Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.… Microsoft.VCRedist.2013.x64                              12.0.40664.0                                     winget 
Microsoft Windows Desktop Runtime - 6.0.36 (x64)         Microsoft.DotNet.DesktopRuntime.6                        6.0.36                                           winget 
vs_CoreEditorFonts                                       ARP\Machine\X86\{1851460E-0E63-4117-B5BA-25A2F045801B}   17.7.40001
Microsoft Visual C++ 2012 Redistributable (x86) - 11.0.… Microsoft.VCRedist.2012.x86                              11.0.61030.0                                     winget 
Windows Software Development Kit - Windows 10.0.22621.3… ARP\Machine\X86\{71684ad3-afc2-4a65-9d45-92ef58510f18}   10.1.22621.3233
Python Launcher                                          Python.Launcher                                          3.12.7                   3.13.5                  winget 
PowerShell 7.5.2.0-x64                                   Microsoft.PowerShell                                     7.5.2.0                                          winget 
Microsoft .NET Runtime - 6.0.36 (x64)                    Microsoft.DotNet.Runtime.6                               6.0.36                                           winget 
Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.… Microsoft.VCRedist.2013.x86                              12.0.40664.0                                     winget 
Adobe Acrobat DC                                         ARP\Machine\X86\{AC76BA86-1033-FFFF-7760-0C0F074E4100}   21.001.20142
Microsoft Visual C++ 2010  x86 Redistributable - 10.0.4… Microsoft.VCRedist.2010.x86                              10.0.40219                                       winget 
Windows SDK AddOn                                        ARP\Machine\X86\{F1E37C98-16B7-421F-BA33-6C5B5400012A}   10.1.0.0
Microsoft Visual C++ 2015-2022 Redistributable (x64) - … Microsoft.VCRedist.2015+.x64                             14.42.34438.0            14.44.35211.0           winget 
Microsoft Visual C++ 2015-2022 Redistributable (x86) - … Microsoft.VCRedist.2015+.x86                             14.42.34438.0            14.44.35211.0           winget 
Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.… Microsoft.VCRedist.2012.x64                              11.0.61030.0                                     winget 
百度网盘                                                 Baidu.BaiduNetdisk                                       7.57.5                   7.59.5                  winget 
7+ Taskbar Tweaker v5.15.3                               RamenSoftware.7+TaskbarTweaker                           5.15.3                                           winget 
DBeaver 25.1.0 (current user)                            DBeaver.DBeaver.Community                                25.1.0                   25.1.4                  winget 
f.lux                                                    flux.flux                                                4.134                                            winget 
剪映专业版                                               ByteDance.JianyingPro                                    8.5.0.13163              8.8.0.13328             winget 
Postman x86_64 11.48.5                                   Postman.Postman                                          11.48.5                  11.58.3                 winget 
uTools 6.1.0                                             Yuanli.uTools                                            6.1.0                    7.2.1                   winget 
Trae (User)                                              ByteDance.Trae                                           1.98.2                   2.0.12                  winget 
Python 3.11.9 (64-bit)                                   Python.Python.3.11                                       3.11.9                                           winget 
Ollama version 0.6.6                                     Ollama.Ollama                                            0.6.6                    0.11.4                  winget 
Microsoft Visual Studio Code (User)                      Microsoft.VisualStudioCode                               1.103.1                                          winget 
Cursor (User)                                            Anysphere.Cursor                                         1.4.3                    1.4.5                   winget 
Python 3.10.11 (64-bit)                                  Python.Python.3.10                                       3.10.11                                          winget 
Python 3.12.7 (64-bit)                                   Python.Python.3.12                                       3.12.7                   3.12.10                 winget 
BaiduNetdiskService                                      MSIX\3AD8A913.49937325EFC59_********_x64__j4r74wbqry4cc  ********
Intel® Graphics Software                                 MSIX\AppUp.IntelArcSoftware_25.26.1602.0_x64__8j3eq9eme… 25.26.1602.0
英特尔® 显卡控制中心                                     MSIX\AppUp.IntelGraphicsExperience_1.100.5688.0_x64__8j… 1.100.5688.0
百度网盘                                                 MSIX\BaiduNetdisk.DesktopSyncClient_********_neutral__r… ********
Microsoft Clipchamp                                      MSIX\Clipchamp.Clipchamp_4.3.10120.0_x64__yxz26nhyzhsrt  4.3.10120.0
AV1 Video Extension                                      MSIX\Microsoft.AV1VideoExtension_********_x64__8wekyb3d… ********
AVC 编码器视频扩展                                       MSIX\Microsoft.AVCEncoderVideoExtension_********_x64__8… ********
Windows 应用程序兼容性增强                               MSIX\Microsoft.ApplicationCompatibilityEnhancements_1.2… 1.2411.16.0
资讯                                                     MSIX\Microsoft.BingNews_4.55.62231.0_x64__8wekyb3d8bbwe  4.55.62231.0
Microsoft 必应                                           MSIX\Microsoft.BingSearch_********_x64__8wekyb3d8bbwe    ********
MSN 天气                                                 MSIX\Microsoft.BingWeather_4.54.63026.0_x64__8wekyb3d8b… 4.54.63026.0
应用安装程序                                             Microsoft.AppInstaller                                   1.26.430.0                                       winget 
Microsoft Edge 游戏助手                                  MSIX\Microsoft.Edge.GameAssist_1.0.3456.0_x64__8wekyb3d… 1.0.3456.0
Xbox                                                     MSIX\Microsoft.GamingApp_2508.1001.27.0_x64__8wekyb3d8b… 2508.1001.27.0
获取帮助                                                 MSIX\Microsoft.GetHelp_10.2409.22951.0_x64__8wekyb3d8bb… 10.2409.22951.0
HEIF 图像扩展                                            MSIX\Microsoft.HEIFImageExtension_1.2.22.0_x64__8wekyb3… 1.2.22.0
来自设备制造商的 HEVC 视频扩展                           MSIX\Microsoft.HEVCVideoExtension_2.4.13.0_x64__8wekyb3… 2.4.13.0
Ink.Handwriting.Main.zh-Hans.1.0                         MSIX\Microsoft.Ink.Handwriting.Main.zh-Hans.1.0_0.850.1… 0.850.1840.0
Ink.Handwriting.zh-Hans.1.0                              MSIX\Microsoft.Ink.Handwriting.zh-Hans.1.0_0.850.1840.0… 0.850.1840.0
Ink.Handwriting.zh-Hans.1.0                              MSIX\Microsoft.Ink.Handwriting.zh-Hans.1.0_0.850.1840.0… 0.850.1840.0
中文(简体)本地体验包                                     MSIX\Microsoft.LanguageExperiencePackzh-CN_26100.18.39.… 26100.18.39.0
MPEG-2 视频扩展                                          MSIX\Microsoft.MPEG2VideoExtension_1.2.10.0_x64__8wekyb… 1.2.10.0
Microsoft Edge                                           MSIX\Microsoft.MicrosoftEdge.Stable_139.0.3405.86_neutr… 139.0.3405.86
Microsoft 365 Copilot                                    MSIX\Microsoft.MicrosoftOfficeHub_19.2508.37081.0_x64__… 19.2508.37081.0
Solitaire & Casual Games                                 MSIX\Microsoft.MicrosoftSolitaireCollection_4.23.7100.0… 4.23.7100.0
Microsoft 便笺                                           MSIX\Microsoft.MicrosoftStickyNotes_6.1.4.0_x64__8wekyb… 6.1.4.0
Microsoft .Net Native Framework Package 1.7              MSIX\Microsoft.NET.Native.Framework.1.7_1.7.27413.0_x64… 1.7.27413.0
Microsoft .Net Native Framework Package 1.7              MSIX\Microsoft.NET.Native.Framework.1.7_1.7.27413.0_x86… 1.7.27413.0
Microsoft .Net Native Framework Package 2.2              MSIX\Microsoft.NET.Native.Framework.2.2_2.2.29512.0_x64… 2.2.29512.0
Microsoft .Net Native Framework Package 2.2              MSIX\Microsoft.NET.Native.Framework.2.2_2.2.29512.0_x86… 2.2.29512.0
Microsoft .Net Native Runtime Package 1.7                MSIX\Microsoft.NET.Native.Runtime.1.7_1.7.27422.0_x64__… 1.7.27422.0
Microsoft .Net Native Runtime Package 1.7                MSIX\Microsoft.NET.Native.Runtime.1.7_1.7.27422.0_x86__… 1.7.27422.0
Microsoft .Net Native Runtime Package 2.2                MSIX\Microsoft.NET.Native.Runtime.2.2_2.2.28604.0_x64__… 2.2.28604.0
Microsoft .Net Native Runtime Package 2.2                MSIX\Microsoft.NET.Native.Runtime.2.2_2.2.28604.0_x86__… 2.2.28604.0
Microsoft.Office.ActionsServer                           MSIX\Microsoft.Office.ActionsServer_16.0.19029.20156_ne… 16.0.19029.20156
OfficePushNotificationsUtility                           MSIX\Microsoft.OfficePushNotificationUtility_16.0.19029… 16.0.19029.20156
OneDrive                                                 MSIX\Microsoft.OneDriveSync_25137.715.1.0_neutral__8wek… 25137.715.1.0
Outlook for Windows                                      MSIX\Microsoft.OutlookForWindows_1.2025.219.400_x64__8w… 1.2025.219.400
画图                                                     MSIX\Microsoft.Paint_11.2506.111.0_x64__8wekyb3d8bbwe    11.2506.111.0
Power Automate                                           MSIX\Microsoft.PowerAutomateDesktop_1.0.1435.0_x64__8we… 1.0.1435.0
Raw Image Extension                                      MSIX\Microsoft.RawImageExtension_2.5.5.0_x64__8wekyb3d8… 2.5.5.0
截图工具                                                 MSIX\Microsoft.ScreenSketch_11.2506.25.0_x64__8wekyb3d8… 11.2506.25.0
Windows 安全中心                                         MSIX\Microsoft.SecHealthUI_1000.27840.1000.0_x64__8weky… 1000.27840.1000.0
Microsoft Engagement Framework                           MSIX\Microsoft.Services.Store.Engagement_10.0.23012.0_x… 10.0.23012.0
Microsoft Engagement Framework                           MSIX\Microsoft.Services.Store.Engagement_10.0.23012.0_x… 10.0.23012.0
“开始体验”应用                                           MSIX\Microsoft.StartExperiencesApp_1.73.0.0_x64__8wekyb… 1.73.0.0
Microsoft Store 体验主机                                 MSIX\Microsoft.StorePurchaseApp_22506.1401.4.0_x64__8we… 22506.1401.4.0
Microsoft To Do                                          MSIX\Microsoft.Todos_0.148.3611.0_x64__8wekyb3d8bbwe     0.148.3611.0
Microsoft.UI.Xaml.2.7                                    Microsoft.UI.Xaml.2.7                                    7.2409.9001.0                                    winget 
Microsoft.UI.Xaml.2.7                                    Microsoft.UI.Xaml.2.7                                    7.2409.9001.0                                    winget 
Microsoft.UI.Xaml.2.8                                    Microsoft.UI.Xaml.2.8                                    8.2501.31001.0                                   winget 
Microsoft.UI.Xaml.2.8                                    Microsoft.UI.Xaml.2.8                                    8.2501.31001.0                                   winget 
Microsoft Visual C++ 2015 UWP Desktop Runtime Package    Microsoft.VCLibs.Desktop.14                              14.0.33728.0                                     winget 
Microsoft Visual C++ 2015 UWP Desktop Runtime Package    Microsoft.VCLibs.Desktop.14                              14.0.33728.0                                     winget 
Microsoft Visual C++ 2015 UWP Runtime Package            MSIX\Microsoft.VCLibs.140.00_14.0.33519.0_x64__8wekyb3d… 14.0.33519.0
Microsoft Visual C++ 2015 UWP Runtime Package            MSIX\Microsoft.VCLibs.140.00_14.0.33519.0_x86__8wekyb3d… 14.0.33519.0
VP9 Video Extensions                                     MSIX\Microsoft.VP9VideoExtensions_1.2.6.0_x64__8wekyb3d… 1.2.6.0
Web 媒体扩展                                             MSIX\Microsoft.WebMediaExtensions_1.2.14.0_x64__8wekyb3… 1.2.14.0
WebP 映像扩展                                            MSIX\Microsoft.WebpImageExtension_1.2.10.0_x64__8wekyb3… 1.2.10.0
Widgets Platform Runtime                                 MSIX\Microsoft.WidgetsPlatformRuntime_1.6.9.0_x64__8wek… 1.6.9.0
Dev Home                                                 MSIX\Microsoft.Windows.DevHome_0.0.0.0_x64__8wekyb3d8bb… 0.0.0.0
Microsoft 照片                                           MSIX\Microsoft.Windows.Photos_2025.11070.8001.0_x64__8w… 2025.11070.8001.0
Windows 时钟                                             MSIX\Microsoft.WindowsAlarms_1.1.29.0_x64__8wekyb3d8bbwe 1.1.29.0
WindowsAppRuntime.1.4                                    MSIX\Microsoft.WindowsAppRuntime.1.4_4000.1309.2056.0_x… 4000.1309.2056.0
WindowsAppRuntime.1.4                                    MSIX\Microsoft.WindowsAppRuntime.1.4_4000.1309.2056.0_x… 4000.1309.2056.0
WindowsAppRuntime.1.5                                    MSIX\Microsoft.WindowsAppRuntime.1.5_5001.373.1736.0_x6… 5001.373.1736.0
WindowsAppRuntime.1.5                                    MSIX\Microsoft.WindowsAppRuntime.1.5_5001.373.1736.0_x8… 5001.373.1736.0
WindowsAppRuntime.1.6                                    MSIX\Microsoft.WindowsAppRuntime.1.6_6000.424.1611.0_x6… 6000.424.1611.0
WindowsAppRuntime.1.6                                    MSIX\Microsoft.WindowsAppRuntime.1.6_6000.457.2140.0_x6… 6000.457.2140.0
WindowsAppRuntime.1.6                                    MSIX\Microsoft.WindowsAppRuntime.1.6_6000.486.517.0_x64… 6000.486.517.0
WindowsAppRuntime.1.6                                    MSIX\Microsoft.WindowsAppRuntime.1.6_6000.519.329.0_x64… 6000.519.329.0
WindowsAppRuntime.1.6                                    MSIX\Microsoft.WindowsAppRuntime.1.6_6000.519.329.0_x86… 6000.519.329.0
WindowsAppRuntime.1.7                                    MSIX\Microsoft.WindowsAppRuntime.1.7_7000.498.2246.0_x6… 7000.498.2246.0
WindowsAppRuntime.1.7                                    MSIX\Microsoft.WindowsAppRuntime.1.7_7000.522.1444.0_x6… 7000.522.1444.0
WindowsAppRuntime.1.7                                    MSIX\Microsoft.WindowsAppRuntime.1.7_7000.522.1444.0_x8… 7000.522.1444.0
Windows 计算器                                           MSIX\Microsoft.WindowsCalculator_11.2502.2.0_x64__8weky… 11.2502.2.0
Windows 相机                                             MSIX\Microsoft.WindowsCamera_2025.2505.2.0_x64__8wekyb3… 2025.2505.2.0
反馈中心                                                 MSIX\Microsoft.WindowsFeedbackHub_1.2506.12104.0_x64__8… 1.2506.12104.0
Windows 记事本                                           MSIX\Microsoft.WindowsNotepad_11.2504.62.0_x64__8wekyb3… 11.2504.62.0
Windows 录音机                                           MSIX\Microsoft.WindowsSoundRecorder_1.1.5.0_x64__8wekyb… 1.1.5.0
Microsoft Store                                          MSIX\Microsoft.WindowsStore_22506.1401.10.0_x64__8wekyb… 22506.1401.10.0
Windows 终端                                             Microsoft.WindowsTerminal                                1.22.12111.0                                     winget
Windows Package Manager Source (winget) V2               MSIX\Microsoft.Winget.Source_2025.815.1007.18_neutral__… 2025.815.1007.18
Xbox TCUI                                                MSIX\Microsoft.Xbox.TCUI_1.24.10001.0_x64__8wekyb3d8bbwe 1.24.10001.0
Game Bar                                                 MSIX\Microsoft.XboxGamingOverlay_7.325.7090.0_x64__8wek… 7.325.7090.0
Xbox Identity Provider                                   MSIX\Microsoft.XboxIdentityProvider_12.115.1001.0_x64__… 12.115.1001.0
Game Speech Window                                       MSIX\Microsoft.XboxSpeechToTextOverlay_1.97.17002.0_x64… 1.97.17002.0
手机连接                                                 MSIX\Microsoft.YourPhone_1.25071.54.0_x64__8wekyb3d8bbwe 1.25071.54.0
Windows 媒体播放器                                       MSIX\Microsoft.ZuneMusic_11.2507.6.0_x64__8wekyb3d8bbwe  11.2507.6.0
快速助手                                                 MSIX\MicrosoftCorporationII.QuickAssist_2.0.29.0_x64__8… 2.0.29.0
适用于 Linux 的 Windows 子系统                           MSIX\MicrosoftCorporationII.WindowsSubsystemForLinux_2.… 2.4.11.0
Windows Web Experience Pack                              MSIX\MicrosoftWindows.Client.WebExperience_525.18101.90… 525.18101.90.0
跨设备体验主机                                           MSIX\MicrosoftWindows.CrossDevice_1.25071.33.0_x64__cw5… 1.25071.33.0
NVIDIA Control Panel                                     MSIX\NVIDIACorp.NVIDIAControlPanel_8.1.968.0_x64__56jyb… 8.1.968.0
Notepad++                                                MSIX\NotepadPlusPlus_1.0.0.0_neutral__7njy0v32s6xk6      1.0.0.0
PDFToolExtension                                         MSIX\PDFShellExt_4.0.0.0_x64__8wekyb3d8bbwe              4.0.0.0
电脑管家右键菜单组件                                     MSIX\QQPCMgrContextMenuPackage_1.0.1.0_x64__ztm5az3tm0y… 1.0.1.0
WinRAR                                                   MSIX\WinRAR.ShellExtension_1.0.0.2_x64__d9ma7nkbkv4rp    1.0.0.2
Local Artificial Intelligence Manager                    MSIX\aimgr_0.20.18.0_x64__8wekyb3d8bbwe                  0.20.18.0
PS C:\AI\api> 




好的，我来帮你找出所有已安装的包，然后从中筛选出彩色样式相关的包。

让我修正命令：

现在让我检查PowerShell中的包：

根据我检查的结果，我找到了你已安装的彩色样式包。以下是具体的包名和安装方法：

## 🎨 已安装的彩色样式包清单

### **WSL/Ubuntu 中的彩色工具：**

1. **Oh My Zsh + Powerlevel10k**
   - `zsh` - Z Shell
   - `oh-my-zsh` - Zsh 框架
   - `powerlevel10k` - 强大的 Zsh 主题
   
2. **彩色命令行工具：**
   - `lolcat` - 彩虹色输出工具
   - `eza` - 现代化的 ls 替代品（彩色文件列表）
   - `python3-colorama` - Python 彩色终端文本库
   - `ruby-paint` - Ruby 终端彩色库
   - `python3-rich` - Python 富文本和美化库
   - `python3-pygments` - 语法高亮库

3. **图像和媒体工具：**
   - `catimg` - 在终端显示图片
   - `jp2a` - 将图片转换为 ASCII 艺术

4. **终端增强：**
   - `tmux` - 终端复用器
   - `byobu` - 终端窗口管理器
   - `vim` - 增强版编辑器

### **Windows 中的终端工具：**

1. **Windows Terminal** - 现代化终端
2. **PowerShell 7.5.2** - 新版 PowerShell

## 📦 在其他电脑上安装这些包的命令

### **Ubuntu/Debian 系统：**
```bash
# 安装基础工具
sudo apt update
sudo apt install -y zsh lolcat eza catimg jp2a tmux byobu vim git curl

# 安装 Oh My Zsh
sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"

# 安装 Powerlevel10k 主题
git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ~/.oh-my-zsh/custom/themes/powerlevel10k

# 安装 Python 彩色库
sudo apt install -y python3-colorama python3-rich python3-pygments

# 安装 Ruby 彩色库
sudo apt install -y ruby-paint
```

### **Windows 系统：**
```powershell
# 使用 winget 安装
winget install Microsoft.WindowsTerminal
winget install Microsoft.PowerShell

# 或使用 Chocolatey
choco install microsoft-windows-terminal
choco install powershell-core
```

### **macOS 系统：**
```bash
# 使用 Homebrew
brew install zsh lolcat eza tmux vim git
brew install --cask iterm2

# 安装 Oh My Zsh
sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"

# 安装 Powerlevel10k
git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ~/.oh-my-zsh/custom/themes/powerlevel10k
```

这些就是你系统中已安装的主要彩色样式包。你可以根据需要在其他电脑上安装相同的工具来获得一致的终端体验！


PS C:\AI\api> wsl -d Ubuntu-24.04 -- bash -c "echo '=== 检查所有可能的彩色工具 ===' && which figlet 2>/dev/null && which toilet 2>/dev/null && which cowsay 2>/dev/null && which fortune 2>/dev/null && which sl 2>/dev/null && which cmatrix 2>/dev/null && which htop 2>/dev/null && which btop 2>/dev/null && which tree 2>/dev/null && which fd 2>/dev/null && which rg 2>/dev/null && which fzf 2>/dev/null && which delta 2>/dev/null && which bat 2>/dev/null && which dust 2>/dev/null && which procs 2>/dev/null && which bandwhich 2>/dev/null && which hyperfine 2>/dev/null && which starship 2>/dev/null && which oh-my-posh 2>/dev/null"
wsl: 检测到 localhost 代理配置，但未镜像到 WSL。NAT 模式下的 WSL 不支持 localhost 代理。
=== 检查所有可能的彩色工具 ===
PS C:\AI\api> 
PS C:\AI\api> wsl -d Ubuntu-24.04 -- bash -c "echo '=== 检查snap包 ===' && snap list 2>/dev/null || echo 'snap未安装或无权限' && echo '=== 检查flatpak包 ===' && flatpak list 2>/dev/null || echo 'flatpak未安装' && echo '=== 检查cargo安装的包 ===' && ls ~/.cargo/bin 2>/dev/null || echo 'cargo bin目录不存在'"                                 
wsl: 检测到 localhost 代理配置，但未镜像到 WSL。NAT 模式下的 WSL 不支持 localhost 代理。                                                                                  
=== 检查snap包 ===
=== 检查flatpak包 ===
flatpak未安装
=== 检查cargo安装的包 ===
cargo bin目录不存在
PS C:\AI\api> 
PS C:\AI\api> wsl -d Ubuntu-24.04 -- bash -c "echo '=== 检查gem安装的包 ===' && gem list 2>/dev/null | grep -E '(color|theme|terminal)' || echo '无相关gem包' && echo '=== 检查npm全局包 ===' && npm list -g --depth=0 2>/dev/null || echo 'npm未安装' && echo '=== 检查pip用户包 ===' && pip list --user 2>/dev/null | grep -E '(color|theme|rich|prompt)' || echo '无相关pip包'"
wsl: 检测到 localhost 代理配置，但未镜像到 WSL。NAT 模式下的 WSL 不支持 localhost 代理。
=== 检查gem安装的包 ===
无相关gem包
=== 检查npm全局包 ===
C:\nvm1\v20.18.0
├── corepack@0.29.3
├── npm@10.8.2
└── pnpm@10.14.0

=== 检查pip用户包 ===
无相关pip包
PS C:\AI\api> 



PS C:\AI\api> powershell -Command "echo '=== 检查PowerShell模块 ==='; Get-InstalledModule | Where-Object {$_.Name -match 'posh|theme|color|terminal|prompt'} | Select-Object Name, Version; echo '=== 检查Chocolatey包 ==='; if (Get-Command choco -ErrorAction SilentlyContinue) { choco list --local-only | Select-String -Pattern 'posh|theme|color|terminal' } else { echo 'Chocolatey未安装' }; echo '=== 检查Scoop包 ==='; if (Get-Command scoop -ErrorAction SilentlyContinue) { scoop list | Select-String -Pattern 'posh|theme|color|terminal' } else { echo 'Scoop未安装' }"
=== 检查PowerShell模块 ===
=== 检查Chocolatey包 ===
Chocolatey未安装
=== 检查Scoop包 ===
Scoop未安装
PS C:\AI\api> 



PS C:\AI\api> wsl -d Ubuntu-24.04 -- bash -c "echo '=== 检查字体 ===' && fc-list | grep -i 'nerd\|powerline\|fira\|hack\|source' | head -10 || echo '无特殊字体' && echo '=== 检查zsh插件 ===' && ls ~/.oh-my-zsh/plugins/ | grep -E '(color|syntax|highlight)' || echo '无相关插件' && echo '=== 检查自定义插件 ===' && ls ~/.oh-my-zsh/custom/plugins/ 2>/dev/null || echo '无自定义插件'"
wsl: 检测到 localhost 代理配置，但未镜像到 WSL。NAT 模式下的 WSL 不支持 localhost 代理。
=== 检查字体 ===
=== 检查zsh插件 ===
colored-man-pages
colorize
=== 检查自定义插件 ===
example
PS C:\AI\api> 



让我更仔细地检查，确保没有遗漏任何彩色样式包。

现在我发现了更多的彩色样式包！让我补充完整的清单：

## 🎨 **完整的彩色样式包清单**

你说得对，我之前遗漏了一些！以下是**完整**的已安装彩色样式包：

### **WSL/Ubuntu 中的彩色工具：**

#### **核心终端美化：**
1. **Oh My Zsh + Powerlevel10k**
   - `zsh` - Z Shell
   - `oh-my-zsh` - Zsh 框架
   - `powerlevel10k` - 强大的 Zsh 主题

#### **Zsh 插件（新发现）：**
2. **Oh My Zsh 彩色插件：**
   - `colored-man-pages` - 彩色手册页
   - `colorize` - 语法高亮插件

#### **彩色命令行工具：**
3. **文件列表美化：**
   - `eza` - 现代化的 ls 替代品（你的配置中有详细的彩色别名）
   - `dircolors` - 目录颜色配置工具

4. **彩色输出工具：**
   - `lolcat` - 彩虹色输出工具
   - `python3-colorama` - Python 彩色终端文本库
   - `ruby-paint` - Ruby 终端彩色库
   - `python3-rich` - Python 富文本和美化库
   - `python3-pygments` - 语法高亮库

#### **图像和媒体工具：**
5. **终端图像显示：**
   - `catimg` - 在终端显示图片
   - `jp2a` - 将图片转换为 ASCII 艺术

#### **终端增强：**
6. **终端管理：**
   - `tmux` - 终端复用器
   - `byobu` - 终端窗口管理器

#### **自定义配置（新发现）：**
7. **彩色别名配置：**
   ```bash
   # 在你的 .zshrc 中发现的彩色配置
   alias ls='eza -la --icons --color=always --group-directories-first'
   alias ll='eza -la --icons --color=always --group-directories-first'
   alias la='eza -la --icons --color=always --group-directories-first'
   alias tree='eza --tree --icons --color=always'
   alias l='eza -l --icons --color=always --group-directories-first'
   
   # 在 .bashrc 中的基础彩色配置
   alias ls='ls --color=auto'
   alias grep='grep --color=auto'
   alias fgrep='fgrep --color=auto'
   alias egrep='egrep --color=auto'
   ```

### **Windows 中的终端工具：**
8. **现代终端：**
   - `Windows Terminal` - 现代化终端
   - `PowerShell 7.5.2` - 新版 PowerShell

### **Node.js 环境：**
9. **包管理器：**
   - `npm` - Node.js 包管理器
   - `pnpm` - 快速的包管理器
   - `nvm` - Node.js 版本管理器

## 📦 **完整安装命令**

### **Ubuntu/Debian 系统完整安装：**
```bash
# 更新系统
sudo apt update

# 安装所有彩色工具
sudo apt install -y zsh lolcat eza catimg jp2a tmux byobu vim git curl tree

# 安装 Python 彩色库
sudo apt install -y python3-colorama python3-rich python3-pygments

# 安装 Ruby 彩色库
sudo apt install -y ruby-paint

# 安装 Oh My Zsh
sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"

# 安装 Powerlevel10k 主题
git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ~/.oh-my-zsh/custom/themes/powerlevel10k

# 配置 .zshrc（添加主题和插件）
echo 'ZSH_THEME="powerlevel10k/powerlevel10k"' >> ~/.zshrc
echo 'plugins=(git colored-man-pages colorize)' >> ~/.zshrc

# 添加彩色别名
cat >> ~/.zshrc << 'EOF'
alias ls='eza -la --icons --color=always --group-directories-first'
alias ll='eza -la --icons --color=always --group-directories-first'
alias la='eza -la --icons --color=always --group-directories-first'
alias tree='eza --tree --icons --color=always'
alias l='eza -l --icons --color=always --group-directories-first'
EOF
```

### **Windows 系统完整安装：**
```powershell
# 安装终端工具
winget install Microsoft.WindowsTerminal
winget install Microsoft.PowerShell

# 安装 Node.js 环境
winget install CoreyButler.NVMforWindows
winget install OpenJS.NodeJS
```

现在这个清单应该是**完整**的了！包括了所有我在你系统中发现的彩色样式包和配置。

