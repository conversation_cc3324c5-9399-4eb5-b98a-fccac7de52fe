const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const AdvancedComponentDetector = require('./advanced-component-detector');
const potrace = require('potrace');
const SVGO = require('svgo');
const ColorThief = require('colorthief');

/**
 * 图片到SVG转换器
 * 用于将UI界面图片转换为结构化可编辑SVG
 */
class ImageToSvgConverter {
  constructor(options = {}) {
    this.options = {
      inputImage: 'image.png',                   // 输入图片
      outputDir: 'public/svg',                   // 输出目录
      debugDir: 'public/debug',                  // 调试输出目录
      colorPaletteSize: 5,                       // 提取的色彩数量
      componentDetectionOptions: {},             // 组件检测选项
      svgOptimize: true,                         // 是否优化SVG
      ...options
    };

    // 创建输出目录
    if (!fs.existsSync(this.options.outputDir)) {
      fs.mkdirSync(this.options.outputDir, { recursive: true });
    }
    
    if (!fs.existsSync(this.options.debugDir)) {
      fs.mkdirSync(this.options.debugDir, { recursive: true });
    }

    // 初始化组件检测器
    this.componentDetector = new AdvancedComponentDetector(
      this.options.componentDetectionOptions
    );
  }

  /**
   * 运行转换流程
   */
  async convert() {
    try {
      console.log('开始图片到SVG的转换过程...');
      const startTime = Date.now();

      // 1. 图片路径解析
      const imagePath = path.resolve(this.options.inputImage);
      if (!fs.existsSync(imagePath)) {
        throw new Error(`输入图片不存在: ${imagePath}`);
      }

      // 2. 加载图片
      const image = await loadImage(imagePath);
      console.log(`图片加载完成，尺寸: ${image.width}x${image.height}`);

      // 3. 提取主要色彩
      const colors = await this.extractColors(imagePath);
      console.log('提取的主题色彩:', colors);

      // 4. 检测UI组件
      const components = await this.componentDetector.detectComponents(imagePath);
      console.log(`检测到 ${components.length} 个UI组件`);

      // 5. 可视化组件检测结果（用于调试）
      const debugImagePath = path.join(this.options.debugDir, 'detected-components.png');
      await this.componentDetector.visualizeComponents(
        imagePath,
        components,
        debugImagePath
      );

      // 6. 为每个组件生成SVG
      const svgComponents = await this.generateComponentSvgs(
        imagePath,
        components,
        image.width,
        image.height
      );
      console.log(`生成了 ${svgComponents.length} 个SVG组件`);

      // 7. 生成主SVG文件
      const mainSvgPath = await this.generateMainSvg(
        svgComponents,
        image.width,
        image.height,
        colors
      );

      // 8. 创建CSS样式文件
      const cssPath = this.generateCssFile(colors, components);

      // 9. 创建使用示例HTML文件
      const htmlPath = this.generateHtmlExample(mainSvgPath, cssPath);

      const endTime = Date.now();
      const timeTaken = (endTime - startTime) / 1000;
      console.log(`转换完成！耗时: ${timeTaken.toFixed(2)}秒`);
      console.log(`输出目录: ${path.resolve(this.options.outputDir)}`);
      console.log(`示例HTML: ${htmlPath}`);

      return {
        mainSvgPath,
        cssPath,
        htmlPath,
        components: svgComponents
      };
    } catch (error) {
      console.error('转换过程出错:', error);
      throw error;
    }
  }

  /**
   * 提取图片中的主要色彩
   * @param {string} imagePath - 图片路径
   * @returns {Array} 主要色彩数组
   */
  async extractColors(imagePath) {
    try {
      const colors = await ColorThief.getPalette(imagePath, this.options.colorPaletteSize);
      return colors.map(color => `rgb(${color[0]}, ${color[1]}, ${color[2]})`);
    } catch (error) {
      console.warn('提取色彩失败，使用默认色彩:', error);
      return [
        'rgb(33, 150, 243)',  // 主色
        'rgb(63, 81, 181)',   // 辅色
        'rgb(255, 255, 255)', // 背景色
        'rgb(33, 33, 33)',    // 文本色
        'rgb(117, 117, 117)'  // 次要文本色
      ];
    }
  }

  /**
   * 为每个组件生成SVG
   * @param {string} imagePath - 原始图片路径
   * @param {Array} components - 组件数组
   * @param {number} imageWidth - 图片宽度
   * @param {number} imageHeight - 图片高度
   * @returns {Array} 带SVG信息的组件数组
   */
  async generateComponentSvgs(imagePath, components, imageWidth, imageHeight) {
    const canvas = createCanvas(imageWidth, imageHeight);
    const ctx = canvas.getContext('2d');

    // 加载原始图片到canvas
    const image = await loadImage(imagePath);
    ctx.drawImage(image, 0, 0);

    // 为每个组件生成SVG
    const svgPromises = components.map(async (component) => {
      const { id, type, x, y, width, height } = component;
      const fileName = `${id}.svg`;
      const filePath = path.join(this.options.outputDir, fileName);

      try {
        // 从原图中裁剪组件区域
        const componentCanvas = createCanvas(width, height);
        const componentCtx = componentCanvas.getContext('2d');

        // 绘制组件区域
        componentCtx.drawImage(
          canvas,
          x, y, width, height,
          0, 0, width, height
        );

        // 转换为SVG
        const svgData = await this.canvasToSvg(componentCanvas, id, type);

        // 保存SVG文件
        fs.writeFileSync(filePath, svgData);

        return {
          ...component,
          svgPath: filePath,
          svgFileName: fileName
        };
      } catch (error) {
        console.error(`生成组件 ${id} 的SVG时出错:`, error);
        return component;
      }
    });

    return Promise.all(svgPromises);
  }

  /**
   * 将Canvas转换为SVG
   * @param {Canvas} canvas - 画布
   * @param {string} id - 组件ID
   * @param {string} type - 组件类型
   * @returns {string} SVG数据
   */
  async canvasToSvg(canvas, id, type) {
    return new Promise((resolve, reject) => {
      // 将canvas转为PNG buffer
      const pngBuffer = canvas.toBuffer('image/png');

      // potrace配置
      const traceOptions = {
        threshold: 128,
        optTolerance: 0.2,
        turdSize: 5,
        turnPolicy: potrace.Potrace.TURNPOLICY_MINORITY,
        color: '#000000',
        background: 'transparent'
      };

      // 根据组件类型调整参数
      if (type === 'icon' || type === 'button') {
        traceOptions.turdSize = 2;
        traceOptions.optTolerance = 0.1;
      }

      // 转换为SVG
      potrace.trace(pngBuffer, traceOptions, (err, svg) => {
        if (err) {
          reject(err);
          return;
        }

        // 如果需要优化SVG
        if (this.options.svgOptimize) {
          const svgo = new SVGO({
            plugins: [
              { removeViewBox: false },
              { cleanupAttrs: true },
              { removeDoctype: true },
              { removeXMLProcInst: true },
              { removeComments: true },
              { removeMetadata: true },
              { removeTitle: true },
              { removeDesc: true },
              { removeUselessDefs: true },
              { removeEditorsNSData: true },
              { removeEmptyAttrs: true },
              { removeHiddenElems: true },
              { removeEmptyText: true },
              { removeEmptyContainers: true },
              { cleanupEnableBackground: true },
              { convertColors: true },
              { convertPathData: true },
              { convertTransform: true },
              { removeDimensions: false },
              { collapseGroups: true },
              { removeUselessStrokeAndFill: true },
              { removeNonInheritableGroupAttrs: true },
              { removeUnusedNS: true },
              { cleanupIDs: true },
              { moveElemsAttrsToGroup: true },
              { moveGroupAttrsToElems: true },
              { mergePaths: true },
              { convertShapeToPath: true },
              { sortAttrs: true },
              { transformsWithOnePath: false },
              { removeRasterImages: false }
            ]
          });

          svgo.optimize(svg).then(result => {
            // 添加组件ID和类型信息
            const svgWithId = result.data.replace(
              '<svg',
              `<svg id="${id}" class="component ${type}" data-component-type="${type}"`
            );
            resolve(svgWithId);
          }).catch(reject);
        } else {
          // 添加组件ID和类型信息
          const svgWithId = svg.replace(
            '<svg',
            `<svg id="${id}" class="component ${type}" data-component-type="${type}"`
          );
          resolve(svgWithId);
        }
      });
    });
  }

  /**
   * 生成主SVG文件
   * @param {Array} components - SVG组件数组
   * @param {number} width - 图片宽度
   * @param {number} height - 图片高度
   * @param {Array} colors - 颜色数组
   * @returns {string} 主SVG文件路径
   */
  async generateMainSvg(components, width, height, colors) {
    const fileName = 'main.svg';
    const filePath = path.join(this.options.outputDir, fileName);

    // 创建主SVG内容
    let svgContent = `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <title>UI界面SVG布局</title>
  <defs>
    <!-- 渐变和滤镜定义 -->
    <linearGradient id="background-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="${colors[0]}" />
      <stop offset="100%" stop-color="${colors[1]}" />
    </linearGradient>
    
    <filter id="drop-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="4" />
      <feOffset dx="2" dy="2" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
    
    <!-- 组件符号定义 -->
    ${components.map(component => {
      if (!component.svgFileName) return '';
      return `<symbol id="${component.id}-symbol">
      <use xlink:href="./${component.svgFileName}" />
    </symbol>`;
    }).join('\n    ')}
  </defs>
  
  <!-- 背景层 -->
  <rect width="${width}" height="${height}" fill="url(#background-gradient)" />
  
  <!-- 组件层 -->
  ${components.map(component => {
    if (!component.svgFileName) return '';
    if (component.type === 'background') return '';
    
    const { id, x, y, width, height } = component;
    return `<g id="${id}-container" class="component-container" transform="translate(${x}, ${y})">
    <use xlink:href="#${id}-symbol" width="${width}" height="${height}" class="${component.type}-component" />
  </g>`;
  }).join('\n  ')}
</svg>`;

    // 保存主SVG文件
    fs.writeFileSync(filePath, svgContent);
    console.log(`主SVG文件已保存: ${filePath}`);

    // 创建组件信息JSON文件
    const componentsInfo = {
      width,
      height,
      colors,
      components: components.map(c => ({
        id: c.id,
        type: c.type,
        x: c.x,
        y: c.y,
        width: c.width,
        height: c.height,
        fileName: c.svgFileName || null
      }))
    };

    const jsonFilePath = path.join(this.options.outputDir, 'components.json');
    fs.writeFileSync(jsonFilePath, JSON.stringify(componentsInfo, null, 2));

    return filePath;
  }

  /**
   * 生成CSS样式文件
   * @param {Array} colors - 颜色数组
   * @param {Array} components - 组件数组
   * @returns {string} CSS文件路径
   */
  generateCssFile(colors, components) {
    const filePath = path.join(this.options.outputDir, 'styles.css');

    // 创建CSS内容
    let cssContent = `/* 自动生成的UI组件样式 */
:root {
  --primary-color: ${colors[0]};
  --secondary-color: ${colors[1]};
  --background-color: ${colors[2] || '#ffffff'};
  --text-color: ${colors[3] || '#333333'};
  --accent-color: ${colors[4] || '#ff5722'};
}

/* 整体容器 */
.svg-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* 组件通用样式 */
.component {
  transition: transform 0.3s ease, filter 0.3s ease;
}

.component:hover {
  filter: brightness(1.1);
}

/* 按组件类型设置样式 */
`;

    // 为每种组件类型添加样式
    const componentTypes = [...new Set(components.map(c => c.type))];
    componentTypes.forEach(type => {
      cssContent += `
.${type}-component {
  ${type === 'button' ? 'cursor: pointer;' : ''}
  ${type === 'panel' ? 'filter: drop-shadow(0 2px 5px rgba(0,0,0,0.1));' : ''}
  ${type === 'icon' ? 'fill: var(--accent-color);' : ''}
}
`;
    });

    // 添加动画和交互样式
    cssContent += `
/* 可选动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.scale-in {
  animation: scaleIn 0.5s ease-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .component-container {
    transform-origin: center;
    transform: scale(0.8);
  }
}
`;

    // 保存CSS文件
    fs.writeFileSync(filePath, cssContent);
    console.log(`CSS样式文件已保存: ${filePath}`);

    return filePath;
  }

  /**
   * 生成示例HTML文件
   * @param {string} svgPath - SVG文件路径
   * @param {string} cssPath - CSS文件路径
   * @returns {string} HTML文件路径
   */
  generateHtmlExample(svgPath, cssPath) {
    const fileName = 'index.html';
    const filePath = path.join(this.options.outputDir, fileName);
    const svgFileName = path.basename(svgPath);
    const cssFileName = path.basename(cssPath);

    const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SVG大屏界面示例</title>
  <link rel="stylesheet" href="./${cssFileName}">
  <style>
    body, html {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    
    .svg-container {
      width: 100%;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f5f5f5;
    }
    
    .controls {
      position: fixed;
      bottom: 20px;
      left: 20px;
      z-index: 100;
      background: rgba(255,255,255,0.8);
      padding: 10px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .toggle-animation {
      margin-right: 10px;
      padding: 5px 10px;
      background: #2196F3;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }
    
    object {
      max-width: 100%;
      max-height: 100%;
    }
  </style>
</head>
<body>
  <div class="svg-container">
    <object type="image/svg+xml" data="./${svgFileName}" id="svg-object" class="scale-in"></object>
  </div>
  
  <div class="controls">
    <button class="toggle-animation" onclick="toggleAnimation()">切换动画</button>
    <button class="toggle-animation" onclick="toggleTheme()">切换主题</button>
  </div>

  <script>
    // 等待SVG加载完成
    document.getElementById('svg-object').addEventListener('load', function() {
      console.log('SVG加载完成');
      initInteractivity();
    });
    
    // 初始化交互功能
    function initInteractivity() {
      const svgDoc = document.getElementById('svg-object').contentDocument;
      if (!svgDoc) return;
      
      // 找到所有组件容器
      const componentContainers = svgDoc.querySelectorAll('.component-container');
      
      // 为每个组件添加动画和交互效果
      componentContainers.forEach(container => {
        container.style.opacity = 0;
        
        // 错开动画时间
        const delay = Math.random() * 1000;
        setTimeout(() => {
          container.style.transition = 'opacity 0.5s ease-in-out';
          container.style.opacity = 1;
        }, delay);
        
        // 添加点击效果
        container.addEventListener('click', function(e) {
          console.log('组件被点击:', this.id);
          this.style.transform = 'scale(1.05)';
          setTimeout(() => {
            this.style.transform = 'scale(1)';
          }, 300);
        });
      });
    }
    
    // 切换动画效果
    function toggleAnimation() {
      const svgObject = document.getElementById('svg-object');
      if (svgObject.classList.contains('scale-in')) {
        svgObject.classList.remove('scale-in');
        svgObject.classList.add('fade-in');
      } else {
        svgObject.classList.remove('fade-in');
        svgObject.classList.add('scale-in');
      }
    }
    
    // 切换主题
    function toggleTheme() {
      const svgDoc = document.getElementById('svg-object').contentDocument;
      if (!svgDoc) return;
      
      // 获取渐变
      const gradient = svgDoc.getElementById('background-gradient');
      if (!gradient) return;
      
      // 获取当前颜色
      const stops = gradient.querySelectorAll('stop');
      const color1 = stops[0].getAttribute('stop-color');
      const color2 = stops[1].getAttribute('stop-color');
      
      // 切换颜色
      stops[0].setAttribute('stop-color', color2);
      stops[1].setAttribute('stop-color', color1);
    }
  </script>
</body>
</html>`;

    fs.writeFileSync(filePath, htmlContent);
    console.log(`示例HTML文件已保存: ${filePath}`);

    return filePath;
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const converter = new ImageToSvgConverter();
  converter.convert().catch(console.error);
}

module.exports = ImageToSvgConverter; 