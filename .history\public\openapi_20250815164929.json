{
  "openapi": "3.0.3",
  "info": {
    "title": "RAGFlow API",
    "description": "RAGFlow的RESTful API完整参考。使用前，请确保您已准备好[RAGFlow API密钥进行身份验证](../guides/models/llm_api_key_setup.md)。",
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "http://*************:6610",
      "description": "开发服务器"
    }
  ],
  "components": {
    "securitySchemes": {
      "bearerAuth": {
        "type": "http",
        "scheme": "bearer",
        "bearerFormat": "API Key"
      }
    },
    "parameters": {
      "DatasetId": {
        "name": "dataset_id",
        "in": "path",
        "required": true,
        "schema": {
          "type": "string",
          "default": "0d677ada090e11f083fcf6ceb56a6e4e"
        },
        "description": "数据集ID"
      },
      "ChatId": {
        "name": "chat_id",
        "in": "path",
        "required": true,
        "schema": {
          "type": "string",
          "default": "56e07344091611f09ea7f6ceb56a6e4e"
        },
        "description": "聊天ID"
      },
      "ApiKey": {
        "name": "Authorization",
        "in": "header",
        "required": true,
        "schema": {
          "type": "string",
          "default": "Bearer ragflow-c2ZmY3MzVhMDkwYzExZjA5MzIwZjZjZW"
        },
        "description": "API密钥"
      }
    }
  },
  "security": [
    {
      "bearerAuth": []
    }
  ],
  "paths": {
    "/api/v1/chats_openai/{chat_id}/chat/completions": {
      "post": {
        "summary": "创建聊天完成",
        "description": "为给定的聊天对话创建模型响应。此API遵循与OpenAI API相同的请求和响应格式，允许您以类似于使用OpenAI API的方式与模型交互。",
        "operationId": "createChatCompletion",
        "parameters": [
          {
            "$ref": "#/components/parameters/ChatId"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["model", "messages"],
                "properties": {
                  "model": {
                    "type": "string",
                    "description": "用于生成响应的模型。服务器将自动解析，因此您现在可以将其设置为任何值。"
                  },
                  "messages": {
                    "type": "array",
                    "description": "用于生成响应的历史聊天消息列表。必须至少包含一条具有'user'角色的消息。",
                    "items": {
                      "type": "object",
                      "properties": {
                        "role": {
                          "type": "string",
                          "enum": ["system", "user", "assistant"],
                          "description": "消息的角色"
                        },
                        "content": {
                          "type": "string",
                          "description": "消息内容"
                        }
                      }
                    }
                  },
                  "stream": {
                    "type": "boolean",
                    "description": "是否以流的形式接收响应。如果您更喜欢一次性接收整个响应而不是流式接收，请将其明确设置为'false'。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功响应",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "id": {
                      "type": "string"
                    },
                    "choices": {
                      "type": "array",
                      "items": {
                        "type": "object",
                        "properties": {
                          "message": {
                            "type": "object",
                            "properties": {
                              "content": {
                                "type": "string"
                              },
                              "role": {
                                "type": "string"
                              }
                            }
                          },
                          "finish_reason": {
                            "type": "string"
                          },
                          "index": {
                            "type": "integer"
                          },
                          "logprobs": {
                            "type": "object",
                            "nullable": true
                          }
                        }
                      }
                    },
                    "created": {
                      "type": "integer"
                    },
                    "model": {
                      "type": "string"
                    },
                    "object": {
                      "type": "string"
                    },
                    "usage": {
                      "type": "object",
                      "properties": {
                        "prompt_tokens": {
                          "type": "integer"
                        },
                        "completion_tokens": {
                          "type": "integer"
                        },
                        "total_tokens": {
                          "type": "integer"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "请求错误",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/datasets": {
      "post": {
        "summary": "创建数据集",
        "description": "创建新的数据集",
        "operationId": "createDataset",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["name"],
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "要创建的数据集的唯一名称。它必须遵循以下要求：允许的字符包括英文字母(a-z, A-Z)、数字(0-9)和下划线(_)，必须以英文字母或下划线开头，最多65,535个字符，不区分大小写。"
                  },
                  "avatar": {
                    "type": "string",
                    "description": "头像的Base64编码。"
                  },
                  "description": {
                    "type": "string",
                    "description": "要创建的数据集的简要描述。"
                  },
                  "embedding_model": {
                    "type": "string",
                    "description": "要使用的嵌入模型的名称。例如：'BAAI/bge-zh-v1.5'"
                  },
                  "permission": {
                    "type": "string",
                    "enum": ["me", "team"],
                    "description": "指定谁可以访问要创建的数据集。可用选项：'me'（默认）：只有您可以管理数据集；'team'：所有团队成员都可以管理数据集。"
                  },
                  "chunk_method": {
                    "type": "string",
                    "enum": ["naive", "manual", "qa", "table", "paper", "book", "laws", "presentation", "picture", "one", "knowledge_graph", "email"],
                    "description": "要创建的数据集的分块方法。"
                  },
                  "parser_config": {
                    "type": "object",
                    "description": "数据集解析器的配置设置。此JSON对象中的属性随所选的'chunk_method'而变化。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功创建数据集",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "创建数据集失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "delete": {
        "summary": "删除数据集",
        "description": "通过ID删除数据集",
        "operationId": "deleteDatasets",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要删除的数据集的ID。如果未指定，将删除所有数据集。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功删除数据集",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "删除数据集失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "get": {
        "summary": "列出数据集",
        "description": "列出所有数据集",
        "operationId": "listDatasets",
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 1
            },
            "description": "指定将显示数据集的页面。默认为1。"
          },
          {
            "name": "page_size",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 30
            },
            "description": "每页的数据集数量。默认为30。"
          },
          {
            "name": "orderby",
            "in": "query",
            "schema": {
              "type": "string",
              "enum": ["create_time", "update_time"],
              "default": "create_time"
            },
            "description": "数据集应该按哪个字段排序。"
          },
          {
            "name": "desc",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            },
            "description": "表示检索到的数据集是否应按降序排序。默认为true。"
          },
          {
            "name": "name",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的数据集的名称。"
          },
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的数据集的ID。"
          }
        ],
        "responses": {
          "200": {
            "description": "成功列出数据集",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "array",
                      "items": {
                        "type": "object"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/datasets/{dataset_id}": {
      "put": {
        "summary": "更新数据集",
        "description": "更新指定数据集的配置",
        "operationId": "updateDataset",
        "parameters": [
          {
            "$ref": "#/components/parameters/DatasetId"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "数据集的修订名称。"
                  },
                  "embedding_model": {
                    "type": "string",
                    "description": "更新的嵌入模型名称。在更新'embedding_model'之前，请确保'chunk_count'为0。"
                  },
                  "chunk_method": {
                    "type": "string",
                    "enum": ["naive", "manual", "qa", "table", "paper", "book", "laws", "presentation", "picture", "one", "knowledge_graph", "email"],
                    "description": "数据集的分块方法。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功更新数据集",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "更新数据集失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/chats": {
      "post": {
        "summary": "创建聊天助手",
        "description": "创建新的聊天助手",
        "operationId": "createChatAssistant",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["name"],
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "聊天助手的名称。"
                  },
                  "avatar": {
                    "type": "string",
                    "description": "头像的Base64编码。"
                  },
                  "dataset_ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "关联数据集的ID。"
                  },
                  "llm": {
                    "type": "object",
                    "description": "要创建的聊天助手的LLM设置。"
                  },
                  "prompt": {
                    "type": "object",
                    "description": "LLM要遵循的指令。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功创建聊天助手",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "创建聊天助手失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "delete": {
        "summary": "删除聊天助手",
        "description": "通过ID删除聊天助手",
        "operationId": "deleteChatAssistants",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要删除的聊天助手的ID。如果未指定，将删除系统中的所有聊天助手。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功删除聊天助手",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "删除聊天助手失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "get": {
        "summary": "列出聊天助手",
        "description": "列出聊天助手。",
        "operationId": "listChatAssistants",
        "parameters": [
            {
              "name": "page",
              "in": "query",
              "schema": {
                "type": "integer",
                "default": 1
              },
              "description": "指定要检索的页面。默认为1。"
            },
            {
              "name": "page_size",
              "in": "query",
              "schema": {
                "type": "integer",
                "default": 30
              },
              "description": "指定每页的记录数。默认为30。"
            },
            {
              "name": "orderby",
              "in": "query",
              "schema": {
                "type": "string",
                "default": "create_time"
              },
              "description": "指定排序字段。默认为create_time。"
            },
            {
              "name": "desc",
              "in": "query",
              "schema": {
                "type": "boolean",
                "default": true
              },
              "description": "指定排序顺序。默认为true（降序）。"
            },
            {
              "name": "name",
              "in": "query",
              "schema": {
                "type": "string"
              },
              "description": "要检索的聊天助手的名称。"
            },
            {
              "name": "id",
              "in": "query",
              "schema": {
                "type": "string"
              },
              "description": "要检索的聊天助手的ID。"
            },
            {
              "$ref": "#/components/parameters/ApiKey"
            }
          ],
          "responses": {
            "200": {
              "description": "成功列出聊天助手",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "code": {
                        "type": "integer"
                      },
                      "data": {
                        "type": "array",
                        "items": {
                          "type": "object"
                        }
                      }
                    }
                  }
                }
              }
            },
            "400": {
              "description": "列出聊天助手失败",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "object",
                    "properties": {
                      "code": {
                        "type": "integer"
                      },
                      "message": {
                        "type": "string"
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/chats/{chat_id}": {
      "put": {
        "summary": "更新聊天助手",
        "description": "更新指定聊天助手的配置",
        "operationId": "updateChatAssistant",
        "parameters": [
          {
            "name": "chat_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "要更新的聊天助手的ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "聊天助手的修订名称。"
                  },
                  "avatar": {
                    "type": "string",
                    "description": "头像的Base64编码。"
                  },
                  "dataset_ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "关联数据集的ID。"
                  },
                  "llm": {
                    "type": "object",
                    "description": "聊天助手的LLM设置。"
                  },
                  "prompt": {
                    "type": "object",
                    "description": "LLM要遵循的指令。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功更新聊天助手",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "更新聊天助手失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/datasets/{dataset_id}/documents": {
      "post": {
        "summary": "上传文档",
        "description": "将文档上传到指定的数据集。",
        "operationId": "uploadDocuments",
        "parameters": [
          {
            "$ref": "#/components/parameters/DatasetId"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "multipart/form-data": {
              "schema": {
                "type": "object",
                "properties": {
                  "file": {
                    "type": "array",
                    "items": {
                      "type": "string",
                      "format": "binary"
                    },
                    "description": "要上传的文档。可以上传多个文件。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功上传文档",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "array",
                      "items": {
                        "type": "object"
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "上传文档失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "delete": {
        "summary": "删除文档",
        "description": "通过ID删除文档。",
        "operationId": "deleteDocuments",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要删除的文档的ID。如果未指定，将删除指定数据集中的所有文档。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功删除文档",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "删除文档失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "get": {
        "summary": "列出文档",
        "description": "列出指定数据集中的文档。",
        "operationId": "listDocuments",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          },
          {
            "name": "keywords",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "用于匹配文档标题的关键词。"
          },
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 1
            },
            "description": "指定将显示文档的页面。默认为1。"
          },
          {
            "name": "page_size",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 30
            },
            "description": "每页的文档数量。默认为30。"
          },
          {
            "name": "orderby",
            "in": "query",
            "schema": {
              "type": "string",
              "enum": ["create_time", "update_time"],
              "default": "create_time"
            },
            "description": "文档应该按哪个字段排序。"
          },
          {
            "name": "desc",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            },
            "description": "表示检索到的文档是否应按降序排序。默认为true。"
          },
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的文档的ID。"
          },
          {
            "name": "name",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的文档的名称。"
          }
        ],
        "responses": {
          "200": {
            "description": "成功列出文档",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "docs": {
                          "type": "array",
                          "items": {
                            "type": "object"
                          }
                        },
                        "total": {
                          "type": "integer"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "列出文档失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/datasets/{dataset_id}/documents/{document_id}": {
      "put": {
        "summary": "更新文档",
        "description": "更新指定文档的配置。",
        "operationId": "updateDocument",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          },
          {
            "name": "document_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "要更新的文档ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "文档的名称。"
                  },
                  "meta_fields": {
                    "type": "object",
                    "description": "文档的元字段。"
                  },
                  "chunk_method": {
                    "type": "string",
                    "enum": ["naive", "manual", "qa", "table", "paper", "book", "laws", "presentation", "picture", "one", "email", "knowledge_graph"],
                    "description": "应用于文档的分块方法。"
                  },
                  "parser_config": {
                    "type": "object",
                    "description": "数据集解析器的配置设置。此JSON对象中的属性随所选的'chunk_method'而变化。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功更新文档",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "更新文档失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "get": {
        "summary": "下载文档",
        "description": "从指定数据集下载文档。",
        "operationId": "downloadDocument",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          },
          {
            "name": "document_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "要下载的文档ID。"
          }
        ],
        "responses": {
          "200": {
            "description": "成功下载文档",
            "content": {
              "application/octet-stream": {
                "schema": {
                  "type": "string",
                  "format": "binary"
                }
              }
            }
          },
          "400": {
            "description": "下载文档失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/datasets/{dataset_id}/chunks": {
      "post": {
        "summary": "解析文档",
        "description": "解析指定数据集中的文档。",
        "operationId": "parseDocuments",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "数据集ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["document_ids"],
                "properties": {
                  "document_ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要解析的文档的ID。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功开始解析文档",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "解析文档失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "delete": {
        "summary": "停止解析文档",
        "description": "停止解析指定的文档。",
        "operationId": "stopParsingDocuments",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["document_ids"],
                "properties": {
                  "document_ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "应停止解析的文档的ID。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功停止解析文档",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "停止解析文档失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/chats/{chat_id}/sessions": {
      "post": {
        "summary": "创建聊天助手会话",
        "description": "创建聊天助手的会话",
        "operationId": "createChatSession",
        "parameters": [
          {
            "$ref": "#/components/parameters/ChatId"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "要创建的聊天会话的名称。"
                  },
                  "user_id": {
                    "type": "string",
                    "description": "可选的用户定义ID。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功创建聊天会话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "创建聊天会话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "get": {
        "summary": "列出聊天助手会话",
        "description": "列出与指定聊天助手关联的会话。",
        "operationId": "listChatSessions",
        "parameters": [
          {
            "name": "chat_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的聊天助手ID。"
          },
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 1
            },
            "description": "指定将显示会话的页面。默认为1。"
          },
          {
            "name": "page_size",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 30
            },
            "description": "每页的会话数量。默认为30。"
          },
          {
            "name": "orderby",
            "in": "query",
            "schema": {
              "type": "string",
              "enum": ["create_time", "update_time"],
              "default": "create_time"
            },
            "description": "会话应该按哪个字段排序。"
          },
          {
            "name": "desc",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            },
            "description": "表示检索到的会话是否应按降序排序。默认为true。"
          },
          {
            "name": "name",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的聊天会话的名称。"
          },
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的聊天会话的ID。"
          },
          {
            "name": "user_id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "创建会话时传入的可选用户定义ID。"
          }
        ],
        "responses": {
          "200": {
            "description": "成功列出聊天会话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "array",
                      "items": {
                        "type": "object"
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "列出聊天会话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "delete": {
        "summary": "删除聊天助手会话",
        "description": "通过ID删除聊天助手的会话。",
        "operationId": "deleteChatSessions",
        "parameters": [
          {
            "name": "chat_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的聊天助手ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要删除的会话的ID。如果未指定，将删除与指定聊天助手关联的所有会话。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功删除聊天会话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "删除聊天会话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/chats/{chat_id}/completions": {
      "post": {
        "summary": "与聊天助手对话",
        "description": "向指定的聊天助手提问以开始AI对话。",
        "operationId": "converseChatAssistant",
        "parameters": [
          {
            "$ref": "#/components/parameters/ChatId"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["question"],
                "properties": {
                  "question": {
                    "type": "string",
                    "description": "开始AI对话的问题。"
                  },
                  "stream": {
                    "type": "boolean",
                    "default": true,
                    "description": "是否以流式方式输出响应。默认为true。"
                  },
                  "session_id": {
                    "type": "string",
                    "description": "会话的ID。如果未提供，将生成一个新会话。"
                  },
                  "user_id": {
                    "type": "string",
                    "description": "可选的用户定义ID。仅当未提供session_id时有效。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功开始与聊天助手的对话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "answer": {
                          "type": "string"
                        },
                        "reference": {
                          "type": "object"
                        },
                        "audio_binary": {
                          "type": "string",
                          "nullable": true
                        },
                        "id": {
                          "type": "string"
                        },
                        "session_id": {
                          "type": "string"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "对话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/chats/{chat_id}/sessions/{session_id}": {
      "put": {
        "summary": "更新聊天助手会话",
        "description": "更新指定聊天助手的会话",
        "operationId": "updateChatSession",
        "parameters": [
          {
            "$ref": "#/components/parameters/ChatId"
          },
          {
            "name": "session_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "要更新的会话ID。"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "会话的修订名称。"
                  },
                  "user_id": {
                    "type": "string",
                    "description": "可选的用户定义ID。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功更新聊天会话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "更新聊天会话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks": {
      "post": {
        "summary": "添加块",
        "description": "向指定数据集中的指定文档添加块。",
        "operationId": "addChunk",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          },
          {
            "name": "document_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的文档ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["content"],
                "properties": {
                  "content": {
                    "type": "string",
                    "description": "块的文本内容。"
                  },
                  "important_keywords": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要与块一起标记的关键术语或短语。"
                  },
                  "questions": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "如果有给定问题，嵌入的块将基于它们。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功添加块",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "chunk": {
                          "type": "object"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "添加块失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "get": {
        "summary": "列出块",
        "description": "列出指定文档中的块。",
        "operationId": "listChunks",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          },
          {
            "name": "document_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的文档ID。"
          },
          {
            "name": "keywords",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "用于匹配块内容的关键词。"
          },
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 1
            },
            "description": "指定将显示块的页面。默认为1。"
          },
          {
            "name": "page_size",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 1024
            },
            "description": "每页的最大块数。默认为1024。"
          },
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的块的ID。"
          }
        ],
        "responses": {
          "200": {
            "description": "成功列出块",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "chunks": {
                          "type": "array",
                          "items": {
                            "type": "object"
                          }
                        },
                        "doc": {
                          "type": "object"
                        },
                        "total": {
                          "type": "integer"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "列出块失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "delete": {
        "summary": "删除块",
        "description": "通过ID删除块。",
        "operationId": "deleteChunks",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          },
          {
            "name": "document_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的文档ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "chunk_ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要删除的块的ID。如果未指定，将删除指定文档的所有块。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功删除块",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "删除块失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/datasets/{dataset_id}/documents/{document_id}/chunks/{chunk_id}": {
      "put": {
        "summary": "更新块",
        "description": "更新指定块的内容或配置。",
        "operationId": "updateChunk",
        "parameters": [
          {
            "name": "dataset_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的数据集ID。"
          },
          {
            "name": "document_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联的文档ID。"
          },
          {
            "name": "chunk_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "要更新的块的ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "content": {
                    "type": "string",
                    "description": "块的文本内容。"
                  },
                  "important_keywords": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要与块一起标记的关键术语或短语列表。"
                  },
                  "available": {
                    "type": "boolean",
                    "default": true,
                    "description": "块在数据集中的可用性状态。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功更新块",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "更新块失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/retrieval": {
      "post": {
        "summary": "检索块",
        "description": "从指定数据集检索块。",
        "operationId": "retrieveChunks",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "required": ["question"],
                "properties": {
                  "question": {
                    "type": "string",
                    "description": "用户查询或查询关键词。"
                  },
                  "dataset_ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要搜索的数据集的ID。如果不设置此参数，请确保设置了'document_ids'。"
                  },
                  "document_ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要搜索的文档的ID。确保所有选定的文档使用相同的嵌入模型。否则，将发生错误。如果不设置此参数，请确保设置了'dataset_ids'。"
                  },
                  "page": {
                    "type": "integer",
                    "default": 1,
                    "description": "指定将显示块的页面。默认为1。"
                  },
                  "page_size": {
                    "type": "integer",
                    "default": 30,
                    "description": "每页的最大块数。默认为30。"
                  },
                  "similarity_threshold": {
                    "type": "number",
                    "format": "float",
                    "default": 0.2,
                    "description": "最小相似度分数。默认为0.2。"
                  },
                  "vector_similarity_weight": {
                    "type": "number",
                    "format": "float",
                    "default": 0.3,
                    "description": "向量余弦相似度的权重。默认为0.3。如果x表示向量余弦相似度的权重，则(1 - x)是词项相似度权重。"
                  },
                  "top_k": {
                    "type": "integer",
                    "default": 1024,
                    "description": "参与向量余弦计算的块数。默认为1024。"
                  },
                  "rerank_id": {
                    "type": "string",
                    "description": "重新排名模型的ID。"
                  },
                  "keyword": {
                    "type": "boolean",
                    "default": false,
                    "description": "是否启用基于关键词的匹配。默认为false。"
                  },
                  "highlight": {
                    "type": "boolean",
                    "default": false,
                    "description": "是否在结果中突出显示匹配的术语。默认为false。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功检索块",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "chunks": {
                          "type": "array",
                          "items": {
                            "type": "object",
                            "properties": {
                              "content": {
                                "type": "string"
                              },
                              "content_ltks": {
                                "type": "string"
                              },
                              "document_id": {
                                "type": "string"
                              },
                              "document_keyword": {
                                "type": "string"
                              },
                              "highlight": {
                                "type": "string"
                              },
                              "id": {
                                "type": "string"
                              },
                              "image_id": {
                                "type": "string"
                              },
                              "important_keywords": {
                                "type": "array",
                                "items": {
                                  "type": "string"
                                }
                              },
                              "kb_id": {
                                "type": "string"
                              },
                              "positions": {
                                "type": "array",
                                "items": {
                                  "type": "string"
                                }
                              },
                              "similarity": {
                                "type": "number",
                                "format": "float"
                              },
                              "term_similarity": {
                                "type": "number",
                                "format": "float"
                              },
                              "vector_similarity": {
                                "type": "number",
                                "format": "float"
                              }
                            }
                          }
                        },
                        "doc_aggs": {
                          "type": "array",
                          "items": {
                            "type": "object",
                            "properties": {
                              "count": {
                                "type": "integer"
                              },
                              "doc_id": {
                                "type": "string"
                              },
                              "doc_name": {
                                "type": "string"
                              }
                            }
                          }
                        },
                        "total": {
                          "type": "integer"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "检索块失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/agents": {
      "get": {
        "summary": "列出代理",
        "description": "列出代理。",
        "operationId": "listAgents",
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 1
            },
            "description": "指定将显示代理的页面。默认为1。"
          },
          {
            "name": "page_size",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 30
            },
            "description": "每页的代理数量。默认为30。"
          },
          {
            "name": "orderby",
            "in": "query",
            "schema": {
              "type": "string",
              "enum": ["create_time", "update_time"],
              "default": "create_time"
            },
            "description": "结果应按哪个属性排序。"
          },
          {
            "name": "desc",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            },
            "description": "表示检索到的代理是否应按降序排序。默认为true。"
          },
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的代理的ID。"
          },
          {
            "name": "name",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的代理的名称。"
          }
        ],
        "responses": {
          "200": {
            "description": "成功列出代理",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "array",
                      "items": {
                        "type": "object"
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "列出代理失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/agents/{agent_id}/sessions": {
      "post": {
        "summary": "创建代理会话",
        "description": "创建代理的会话。",
        "operationId": "createAgentSession",
        "parameters": [
          {
            "name": "agent_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联代理的ID。"
          },
          {
            "name": "user_id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "创建会话同时上传文件时解析文档（特别是图像）的可选用户定义ID。"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": false,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "lang": {
                    "type": "string",
                    "description": "目标语言。"
                  },
                  "file": {
                    "type": "string",
                    "description": "文件内容。"
                  }
                }
              }
            },
            "multipart/form-data": {
              "schema": {
                "type": "object",
                "properties": {
                  "file": {
                    "type": "string",
                    "format": "binary",
                    "description": "要上传的文件。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功创建代理会话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "创建代理会话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "get": {
        "summary": "列出代理会话",
        "description": "列出与指定代理关联的会话。",
        "operationId": "listAgentSessions",
        "parameters": [
          {
            "name": "agent_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联代理的ID。"
          },
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 1
            },
            "description": "指定将显示会话的页面。默认为1。"
          },
          {
            "name": "page_size",
            "in": "query",
            "schema": {
              "type": "integer",
              "default": 30
            },
            "description": "每页的会话数量。默认为30。"
          },
          {
            "name": "orderby",
            "in": "query",
            "schema": {
              "type": "string",
              "enum": ["create_time", "update_time"],
              "default": "create_time"
            },
            "description": "会话应该按哪个字段排序。"
          },
          {
            "name": "desc",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            },
            "description": "表示检索到的会话是否应按降序排序。默认为true。"
          },
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "要检索的代理会话的ID。"
          },
          {
            "name": "user_id",
            "in": "query",
            "schema": {
              "type": "string"
            },
            "description": "创建会话时传入的可选用户定义ID。"
          },
          {
            "name": "dsl",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            },
            "description": "是否在响应中包含会话的dsl字段。默认为true。"
          }
        ],
        "responses": {
          "200": {
            "description": "成功列出代理会话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "array",
                      "items": {
                        "type": "object"
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "列出代理会话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      },
      "delete": {
        "summary": "删除代理会话",
        "description": "通过ID删除代理的会话。",
        "operationId": "deleteAgentSessions",
        "parameters": [
          {
            "name": "agent_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联代理的ID。"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "ids": {
                    "type": "array",
                    "items": {
                      "type": "string"
                    },
                    "description": "要删除的会话的ID。如果未指定，将删除与指定代理关联的所有会话。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功删除代理会话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "删除代理会话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/v1/agents/{agent_id}/completions": {
      "post": {
        "summary": "与代理对话",
        "description": "向指定的代理提问以开始AI对话。",
        "operationId": "converseWithAgent",
        "parameters": [
          {
            "name": "agent_id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            },
            "description": "关联代理的ID。"
          },
          {
            "$ref": "#/components/parameters/ApiKey"
          }
        ],
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "question": {
                    "type": "string",
                    "description": "开始AI对话的问题。"
                  },
                  "stream": {
                    "type": "boolean",
                    "default": true,
                    "description": "是否以流式方式输出响应。默认为true。"
                  },
                  "session_id": {
                    "type": "string",
                    "description": "会话的ID。如果未提供，将生成一个新会话。"
                  },
                  "user_id": {
                    "type": "string",
                    "description": "可选的用户定义ID。仅当未提供session_id时有效。"
                  },
                  "sync_dsl": {
                    "type": "boolean",
                    "default": false,
                    "description": "修改代理时是否将更改同步到现有会话，默认为false。"
                  },
                  "lang": {
                    "type": "string",
                    "description": "目标语言。"
                  },
                  "file": {
                    "type": "string",
                    "description": "文件内容。"
                  }
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "成功开始与代理的对话",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "data": {
                      "type": "object",
                      "properties": {
                        "answer": {
                          "type": "string"
                        },
                        "reference": {
                          "type": "object"
                        },
                        "id": {
                          "type": "string"
                        },
                        "session_id": {
                          "type": "string"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "400": {
            "description": "对话失败",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": {
                      "type": "integer"
                    },
                    "message": {
                      "type": "string"
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
} 