# RAGFlow API 自动转换工具使用指南

## 🎯 概述

RAGFlow API自动转换工具是一个强大的命令行工具，能够将Markdown格式的API文档自动转换为符合OpenAPI 3.0.3标准的JSON规范。该工具经过精心设计，确保转换的准确性和完整性。

## ✨ 主要特性

- **🔄 智能解析**：自动识别和解析Markdown中的API端点定义
- **📋 完整转换**：生成符合OpenAPI 3.0.3标准的完整JSON规范
- **🔍 质量保证**：内置多层验证机制确保转换质量
- **⚡ 增量更新**：支持保留现有自定义修改的增量更新模式
- **⚙️ 高度可配置**：通过配置文件自定义转换行为
- **📊 详细报告**：提供详细的转换统计和验证报告

## 🚀 快速开始

### 基本使用

```bash
# 使用默认设置进行转换
node convert.js

# 或使用npm脚本
npm run convert
```

### 指定文件路径

```bash
# 指定输入和输出文件
node convert.js input.md output.json

# 示例
node convert.js http_api_reference.md public/openapi.json
```

## 📖 详细使用方法

### 命令行选项

```bash
# 显示帮助信息
node convert.js --help

# 仅验证现有OpenAPI文件（不进行转换）
node convert.js --validate

# 增量更新模式（保留现有自定义修改）
node convert.js --incremental

# 使用自定义配置文件
node convert.js --config my-config.json
```

### NPM脚本

项目提供了便捷的npm脚本：

```bash
npm run convert              # 执行转换
npm run convert:help         # 显示帮助信息
npm run convert:validate     # 验证现有OpenAPI文件
npm run convert:incremental  # 增量更新模式
npm run analyze              # 分析转换结果
npm run check-responses      # 检查响应模式
npm run validate-all         # 运行所有验证工具
npm run build               # 转换并验证（推荐用于CI/CD）
npm run dev                 # 转换并启动开发服务器
```

## ⚙️ 配置文件

转换工具通过`converter-config.json`文件进行配置。主要配置项包括：

### OpenAPI基本信息
```json
{
  "openapi": {
    "version": "3.0.3",
    "info": {
      "title": "RAGFlow API",
      "description": "API描述",
      "version": "1.0.0"
    },
    "servers": [
      {
        "url": "http://localhost:6610",
        "description": "开发服务器"
      }
    ]
  }
}
```

### 资源映射
```json
{
  "resourceMapping": {
    "datasets": "数据集",
    "documents": "文档",
    "chunks": "块",
    "chats": "聊天助手"
  }
}
```

### 通用参数定义
```json
{
  "commonParameters": {
    "page": {
      "name": "page",
      "in": "query",
      "schema": {
        "type": "integer",
        "default": 1
      },
      "description": "页码"
    }
  }
}
```

## 🔍 质量保证机制

### 多层验证

1. **语法验证**：确保生成的JSON符合OpenAPI规范
2. **完整性检查**：验证所有API端点都被正确转换
3. **模式验证**：确保响应模式定义完整
4. **结构验证**：检查必需字段和推荐字段

### 自动备份

转换工具会在覆盖现有文件前自动创建备份：
```
public/openapi.json → public/openapi.backup.1692123456789.json
```

### 验证报告

转换完成后，工具会生成详细的验证报告：
- API端点统计
- 转换完整性分析
- 响应模式验证
- 潜在问题警告

## 🛠️ 高级功能

### 增量更新模式

增量更新模式允许您在保留现有自定义修改的同时更新API规范：

```bash
node convert.js --incremental
```

该模式会：
- 保留自定义的服务器配置
- 保留自定义的模式定义
- 保留路径级别的自定义修改（如标签、示例等）
- 更新API端点定义

### 自定义配置

创建自定义配置文件来适应特定需求：

```bash
# 使用自定义配置
node convert.js --config production-config.json
```

### 批量处理

可以通过脚本批量处理多个文件：

```bash
# 处理多个API文档
for file in docs/*.md; do
  node convert.js "$file" "output/$(basename "$file" .md).json"
done
```

## 📊 转换统计示例

```
🎉 转换成功完成！
⏰ 完成时间: 2025/8/15 17:05:25
📊 转换统计: 17 个路径，31 个方法
📁 文件大小: 64.46 KB

验证结果:
✅ API端点数量匹配 (31/31)
✅ 所有必需字段都存在
✅ 所有路径定义都完整
⚠️  1 个响应使用非JSON格式（文件下载）
```

## 🔧 故障排除

### 常见问题

1. **转换失败**
   - 检查输入文件是否存在
   - 确认Markdown格式符合预期
   - 查看错误日志获取详细信息

2. **API端点缺失**
   - 检查Markdown中的端点格式是否正确
   - 确认使用了标准的格式：`**METHOD** \`/path\``

3. **配置文件错误**
   - 验证JSON格式是否正确
   - 检查必需的配置项是否存在

### 调试模式

启用详细日志输出：
```bash
DEBUG=1 node convert.js
```

## 🚀 集成到CI/CD

### GitHub Actions示例

```yaml
name: API Documentation
on: [push, pull_request]

jobs:
  convert-and-validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm run build  # 转换并验证
      - run: npm run validate-all
```

### 预提交钩子

```bash
#!/bin/sh
# .git/hooks/pre-commit
npm run build
if [ $? -ne 0 ]; then
  echo "API文档转换失败，请检查并修复问题"
  exit 1
fi
```

## 📝 最佳实践

1. **定期验证**：使用`npm run validate-all`定期验证API文档
2. **版本控制**：将生成的OpenAPI文件纳入版本控制
3. **自动化**：在CI/CD流程中集成转换和验证
4. **备份重要**：重要修改前先创建备份
5. **增量更新**：有自定义修改时使用增量更新模式

## 🤝 贡献指南

欢迎贡献改进建议和代码：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用Apache 2.0许可证。详见LICENSE文件。
